<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\PositionsGroupModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\AppxApplicationProfileModel;
use App\Models\AppxApplicationRatingModel;
use App\Models\RateItemsModel;

/**
 * ReportsForm37Controller
 * 
 * Controller for Form 3.7 Profiling reports with navigation flow:
 * Exercise -> Position Groups -> Positions -> Form 3.7 Report
 */
class ReportsForm37Controller extends Controller
{
    protected $exerciseModel;
    protected $positionGroupModel;
    protected $positionModel;
    protected $applicationModel;
    protected $profileModel;
    protected $ratingModel;
    protected $rateItemsModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionsModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->profileModel = new AppxApplicationProfileModel();
        $this->ratingModel = new AppxApplicationRatingModel();
        $this->rateItemsModel = new RateItemsModel();
    }

    /**
     * [GET] Form 3.7 Report - List Position Groups for Exercise
     * URI: /reports/form37/{exerciseId}
     */
    public function form37($exerciseId)
    {
        // Get exercise data
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get position groups with position counts for this exercise
        $positionGroups = $this->positionGroupModel->getPositionGroupsWithCountByExerciseId($exerciseId);

        $data = [
            'title' => 'Form 3.7 Profiling Report - ' . $exercise['exercise_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'position_groups' => $positionGroups
        ];

        return view('application_reports/appx_reports_form37_groups', $data);
    }

    /**
     * [GET] List Positions in Position Group
     * URI: /reports/form37/groups/{groupId}
     */
    public function groups($groupId)
    {
        // Get position group data
        $positionGroup = $this->positionGroupModel->find($groupId);
        if (!$positionGroup) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position group not found');
        }

        // Get exercise data
        $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get positions with application counts for this group
        $positions = $this->positionModel->getPositionsByGroupIdWithApplicationCount($groupId);

        $data = [
            'title' => 'Form 3.7 Profiling - Positions in ' . $positionGroup['group_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'position_group' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_reports/appx_reports_form37_positions', $data);
    }

    /**
     * [GET] Form 3.7 Profiling Report for Position
     * URI: /reports/form37/positions/{positionId}
     */
    public function positions($positionId)
    {
        // Get position data with related information
        $position = $this->positionModel->select('
                positions.*,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->find($positionId);

        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Get applications for this position
        $applications = $this->applicationModel->where('position_id', $positionId)
            ->orderBy('last_name', 'ASC')
            ->findAll();

        // Get exercise ID for profile lookup
        $exerciseId = $position['exercise_id'];

        // Enhance applications with profile data and ratings
        $enhancedApplications = [];
        foreach ($applications as $application) {
            // Get profile data for this applicant and exercise using applicant_id
            $profile = $this->profileModel->getProfiledApplicantDetails($application['applicant_id'], $exerciseId);

            // Get rating data for this application using application ID
            $ratings = $this->ratingModel->getRatingsWithJustificationsByApplicationId($application['id']);
            $ratingSummary = $this->ratingModel->getRatingSummary($application['id']);

            // Combine application with profile and rating data
            $enhancedApplication = [
                'id' => $application['id'], // Keep application ID for ratings
                'applicant_id' => $application['applicant_id'], // Keep applicant ID for reference
                'profile' => $profile,
                'ratings' => $ratings,
                'rating_summary' => $ratingSummary,
                'profile_status' => $profile['remarks'] ?? 'Not Profiled'
            ];

            $enhancedApplications[] = $enhancedApplication;
        }

        // Group applications by status for better organization
        $groupedApplications = [
            'shortlisted' => [],
            'eliminated' => [],
            'withdrawn' => [],
            'others' => []
        ];

        foreach ($enhancedApplications as $application) {
            $status = strtolower($application['profile_status'] ?? 'others');
            if (strpos($status, 'shortlist') !== false) {
                $groupedApplications['shortlisted'][] = $application;
            } elseif (strpos($status, 'eliminat') !== false) {
                $groupedApplications['eliminated'][] = $application;
            } elseif (strpos($status, 'withdraw') !== false) {
                $groupedApplications['withdrawn'][] = $application;
            } else {
                $groupedApplications['others'][] = $application;
            }
        }

        // Combine in priority order: shortlisted, eliminated, withdrawn, others
        $sortedApplications = array_merge(
            $groupedApplications['shortlisted'],
            $groupedApplications['eliminated'],
            $groupedApplications['withdrawn'],
            $groupedApplications['others']
        );

        // Get all rate items for the rating table
        $rateItems = $this->rateItemsModel->getAllItems();

        $data = [
            'title' => 'Form 3.7 Profiling Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'applications' => $sortedApplications,
            'total_applications' => count($applications),
            'rate_items' => $rateItems
        ];

        return view('application_reports/appx_reports_form37_report', $data);
    }

    /**
     * [POST] Export Form 3.7 Profiling Report to PDF
     * URI: /reports/form37/positions/export
     */
    public function exportForm37()
    {
        // Get and validate parameters
        $positionId = $this->request->getPost('position_id');
        if (!$positionId) {
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Position ID is required'
            ]);
        }

        try {
            // Get position data with related information
            $position = $this->positionModel->select('
                    positions.*,
                    positions_groups.group_name,
                    positions_groups.exercise_id,
                    exercises.exercise_name,
                    dakoii_org.org_name
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->find($positionId);

            if (!$position) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Position not found'
                ]);
            }

            // Get applications for this position
            $applications = $this->applicationModel->where('position_id', $positionId)
                ->orderBy('last_name', 'ASC')
                ->findAll();

            // Get exercise ID for profile lookup
            $exerciseId = $position['exercise_id'];

            // Enhance applications with profile data and ratings
            $enhancedApplications = [];
            foreach ($applications as $application) {
                // Get profile data for this applicant and exercise using applicant_id
                $profile = $this->profileModel->getProfiledApplicantDetails($application['applicant_id'], $exerciseId);

                // Get rating data for this application using application ID
                $ratings = $this->ratingModel->getRatingsWithJustificationsByApplicationId($application['id']);
                $ratingSummary = $this->ratingModel->getRatingSummary($application['id']);

                // Combine application with profile and rating data
                $enhancedApplication = [
                    'id' => $application['id'], // Keep application ID for ratings
                    'applicant_id' => $application['applicant_id'], // Keep applicant ID for reference
                    'profile' => $profile,
                    'ratings' => $ratings,
                    'rating_summary' => $ratingSummary,
                    'profile_status' => $profile['remarks'] ?? 'Not Profiled'
                ];

                $enhancedApplications[] = $enhancedApplication;
            }

            // Get all rate items for the rating table
            $rateItems = $this->rateItemsModel->getAllItems();

            // Generate and output PDF directly to browser
            $this->generateForm37PDF($position, $enhancedApplications, $rateItems);

        } catch (\Exception $e) {
            log_message('error', 'Form 3.7 PDF Export Error: ' . $e->getMessage());
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate Form 3.7 Profiling PDF
     *
     * @param array $position Position data
     * @param array $applications Applications data with profiles and ratings
     * @param array $rateItems Rate items for scoring table
     */
    private function generateForm37PDF($position, $applications, $rateItems)
    {
        try {
            // Create TCPDF with custom footer (Landscape orientation)
            $pdf = new class('L', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    $this->SetY(-18);
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = $this->getPageWidth() - 20; // Account for left and right margins (10mm each)

                    // Draw PNG flag colored sections
                    $this->SetFillColor(240, 15, 0); // Red
                    $this->Rect(10, $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(0, 0, 0); // Black
                    $this->Rect(10 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(255, 194, 15); // Gold
                    $this->Rect(10 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Add content
                    $this->SetFont('helvetica', '', 8);
                    $this->SetY($footerY + 1);

                    // Row 1
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell($footerWidth / 3, 3, 'Generated by DERS System v1.0', 0, 0, 'L');

                    $this->SetX(10 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                    $this->SetX(10 + (2 * $footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . ' of ' . $this->getAliasNbPages(), 0, 0, 'C');

                    // Row 2
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell($footerWidth / 3, 3, 'AI-Powered', 0, 0, 'L');

                    $this->SetX(10 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Row 3
                    $this->SetY($this->GetY() + 3);
                    $this->SetX(10);
                    $this->Cell($footerWidth, 3, 'ders.dakoiims.com', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Configure PDF
            $pdf->SetCreator('DERS System');
            $pdf->SetTitle('Form 3.7 Profiling Report - ' . $position['designation']);
            $pdf->SetMargins(10, 20, 10);
            $pdf->SetAutoPageBreak(true, 25);
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);

            // Add page
            $pdf->AddPage();

            // Add logo
            $logoPath = FCPATH . 'public/assets/system_img/system-logo.png';
            if (file_exists($logoPath)) {
                $pdf->Image($logoPath, ($pdf->getPageWidth() - 20) / 2, $pdf->GetY(), 20, 20);
                $pdf->Ln(25);
            }

            // Add title
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, 'APPLICANT PROFILE REPORT', 0, 1, 'C');
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'FORM RS 3.7', 0, 1, 'R');
            $pdf->Ln(5);

            // Add content
            $this->addForm37Content($pdf, $position, $applications, $rateItems);

            // Generate filename and output directly to browser
            $filename = 'form_37_profiling_' . $position['position_reference'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output($filename, 'D');

        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add Form 3.7 content to PDF
     *
     * @param object $pdf TCPDF instance
     * @param array $position Position data
     * @param array $applications Applications data with profiles and ratings
     * @param array $rateItems Rate items for scoring table
     */
    private function addForm37Content($pdf, $position, $applications, $rateItems)
    {
        // Position Information
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Position Information', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        // Use two-column layout
        $leftColWidth = ($pdf->getPageWidth() - 20) / 2 - 5;
        $rightColWidth = ($pdf->getPageWidth() - 20) / 2 - 5;

        // Left column
        $startY = $pdf->GetY();
        $pdf->Cell(50, 6, 'Position Reference:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['position_reference'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Designation:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['designation'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Classification:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['classification'], 0, 1, 'L');

        // Right column
        $pdf->SetXY(10 + $leftColWidth + 10, $startY);

        $pdf->Cell(50, 6, 'Exercise:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['exercise_name'], 0, 1, 'L');

        $pdf->SetX(10 + $leftColWidth + 10);
        $pdf->Cell(50, 6, 'Position Group:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['group_name'], 0, 1, 'L');

        $pdf->SetX(10 + $leftColWidth + 10);
        $pdf->Cell(50, 6, 'Organization:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['org_name'], 0, 1, 'L');

        // Reset to full width and add spacing
        $pdf->SetX(10);
        $pdf->Ln(8);

        // Applications Summary
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Applications Summary', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        $pdf->Cell(50, 6, 'Total Applications:', 0, 0, 'L');
        $pdf->Cell(0, 6, count($applications), 0, 1, 'L');

        $pdf->Ln(5);

        // Process applications in groups of 3 per page for better readability
        $applicantsPerPage = 3;
        $totalPages = ceil(count($applications) / $applicantsPerPage);

        for ($page = 1; $page <= $totalPages; $page++) {
            if ($page > 1) {
                $pdf->AddPage();
            }

            $startIndex = ($page - 1) * $applicantsPerPage;
            $pageApplicants = array_slice($applications, $startIndex, $applicantsPerPage);

            // Page header with spacing
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Comparative Analysis - Page ' . $page . ' of ' . $totalPages, 0, 1, 'L');
            $pdf->Ln(3);

            // Generate comparative table for this page with improved spacing
            $this->generateComparativeTable($pdf, $position, $pageApplicants, $startIndex);

            // Add rating table for this page with proper spacing
            if (!empty($rateItems)) {
                $this->generateRatingTable($pdf, $pageApplicants, $rateItems, $startIndex);
            }
        }
    }

    /**
     * Generate comparative table for Form 3.7
     *
     * @param object $pdf TCPDF instance
     * @param array $position Position data
     * @param array $pageApplicants Applicants for this page
     * @param int $startIndex Starting index for applicant numbering
     */
    private function generateComparativeTable($pdf, $position, $pageApplicants, $startIndex)
    {
        $pdf->SetFont('helvetica', '', 8);

        // Calculate table width to fit full content width
        $contentWidth = $pdf->getPageWidth() - 20;
        $col1Width = 65;  // Position Specification - slightly wider
        $remainingWidth = $contentWidth - $col1Width;
        $colWidth = $remainingWidth / count($pageApplicants); // Person Specification columns

        // Table header with better formatting
        $pdf->SetFillColor(240, 240, 240);
        $pdf->SetFont('helvetica', 'B', 8);

        // Position specification header
        $pdf->MultiCell($col1Width, 10, 'POSITION SPECIFICATION', 1, 'C', true, 0, '', '', true, 0, false, true, 10, 'M');

        // Person specification headers
        for ($i = 0; $i < count($pageApplicants); $i++) {
            $headerText = "PERSON SPECIFICATION\nApplicant " . ($startIndex + $i + 1);
            $pdf->MultiCell($colWidth, 10, $headerText, 1, 'C', true, 0, '', '', true, 0, false, true, 10, 'M');
        }
        $pdf->Ln();

        // Personal Information Section
        $this->addTableSection($pdf, 'Personal Information', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return "Name: " . ($applicant['profile']['full_name'] ?? 'N/A') . "\n" .
                   "Sex: " . ($applicant['profile']['sex'] ?? 'N/A') . "\n" .
                   "Age: " . ($applicant['profile']['bdate_age'] ?? 'N/A') . "\n" .
                   "Place of Origin: " . ($applicant['profile']['place_origin'] ?? 'N/A') . "\n" .
                   "Current Position: " . ($applicant['profile']['current_position'] ?? 'N/A') . "\n" .
                   "Current Employer: " . ($applicant['profile']['current_employer'] ?? 'N/A') . "\n" .
                   "Address: " . ($applicant['profile']['address_location'] ?? 'N/A');
        }, 'Personal details');

        // Qualifications Section
        $this->addTableSection($pdf, 'Qualifications', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['qualification_text'] ?? 'Not provided';
        }, $position['qualifications'] ?? 'Not specified');

        // Other Training Courses Section
        $this->addTableSection($pdf, 'Other Training Courses', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['other_trainings'] ?? 'Not provided';
        }, 'N/A');

        // Skills & Competencies Section
        $this->addTableSection($pdf, 'Skills & Competencies', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['skills_competencies'] ?? 'Not provided';
        }, $position['skills_competencies'] ?? 'Not specified');

        // Job Experiences Section
        $this->addTableSection($pdf, 'Job Experiences', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['job_experiences'] ?? 'Not provided';
        }, $position['job_experiences'] ?? 'Not specified');

        // Knowledge Section
        $this->addTableSection($pdf, 'Knowledge', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['knowledge'] ?? 'Not provided';
        }, $position['knowledge'] ?? 'Not specified');

        // Remarks Section
        $this->addTableSection($pdf, 'Remarks', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['remarks'] ?? 'Not provided';
        }, $position['remarks'] ?? 'Not specified');
    }

    /**
     * Add a table section with header and content rows with proper page break handling
     */
    private function addTableSection($pdf, $sectionTitle, $position, $pageApplicants, $col1Width, $colWidth, $personDataCallback, $positionData)
    {
        // Calculate row height based on content
        $maxLines = 1;
        $positionLines = $pdf->getNumLines($positionData, $col1Width - 4);
        $maxLines = max($maxLines, $positionLines);

        foreach ($pageApplicants as $applicant) {
            $personData = $personDataCallback($position, $applicant);
            $personLines = $pdf->getNumLines($personData, $colWidth - 4);
            $maxLines = max($maxLines, $personLines);
        }

        $rowHeight = max(12, $maxLines * 4 + 4);
        $headerHeight = 8;
        $totalSectionHeight = $headerHeight + $rowHeight;

        // Check if section fits on current page
        $currentY = $pdf->GetY();
        $pageHeight = $pdf->getPageHeight();
        $bottomMargin = 25; // Footer space
        $availableSpace = $pageHeight - $currentY - $bottomMargin;

        // If section doesn't fit, start new page
        if ($totalSectionHeight > $availableSpace) {
            $pdf->AddPage();
            $currentY = $pdf->GetY();
        }

        // Header row
        $pdf->SetFillColor(250, 250, 250);
        $pdf->SetFont('helvetica', 'B', 9);

        // Check if we need to split the header
        $startX = $pdf->GetX();
        $startY = $pdf->GetY();

        $pdf->Cell($col1Width, $headerHeight, $sectionTitle, 1, 0, 'L', true);
        foreach ($pageApplicants as $index => $applicant) {
            $pdf->Cell($colWidth, $headerHeight, 'Applicant details', 1, 0, 'C', true);
        }
        $pdf->Ln();

        // Content row with proper text wrapping
        $pdf->SetFont('helvetica', '', 8);
        $contentStartY = $pdf->GetY();

        // Position specification column
        $pdf->SetXY($startX, $contentStartY);
        $this->addMultiCellWithBorder($pdf, $col1Width, $rowHeight, $positionData, 1, 'L');

        // Person specification columns
        $currentX = $startX + $col1Width;
        foreach ($pageApplicants as $applicant) {
            $personData = $personDataCallback($position, $applicant);
            $pdf->SetXY($currentX, $contentStartY);
            $this->addMultiCellWithBorder($pdf, $colWidth, $rowHeight, $personData, 1, 'L');
            $currentX += $colWidth;
        }

        // Move to next row
        $pdf->SetXY($startX, $contentStartY + $rowHeight);
    }

    /**
     * Add MultiCell with border that handles page breaks properly
     */
    private function addMultiCellWithBorder($pdf, $width, $height, $text, $border, $align)
    {
        $startX = $pdf->GetX();
        $startY = $pdf->GetY();

        // Clean the text
        $text = trim($text);
        if (empty($text)) {
            $text = 'N/A';
        }

        // Calculate actual height needed
        $lines = $pdf->getNumLines($text, $width - 4);
        $actualHeight = max($height, $lines * 4 + 4);

        // Check if content fits on current page
        $pageHeight = $pdf->getPageHeight();
        $bottomMargin = 25;
        $availableSpace = $pageHeight - $startY - $bottomMargin;

        if ($actualHeight > $availableSpace && $availableSpace > 20) {
            // Content doesn't fit, but we have some space - fill remaining space and continue on next page
            $remainingHeight = $availableSpace - 5;

            // Draw border for current page portion
            $pdf->Rect($startX, $startY, $width, $remainingHeight, 'D');

            // Add partial text
            $pdf->SetXY($startX + 2, $startY + 2);
            $pdf->MultiCell($width - 4, 4, $text, 0, $align, false, 1, '', '', true, 0, false, true, $remainingHeight - 4, 'T');

            // Move to next page
            $pdf->AddPage();
            $newY = $pdf->GetY();

            // Continue with remaining content on new page
            $remainingContentHeight = $actualHeight - $remainingHeight;
            $pdf->SetXY($startX, $newY);
            $pdf->Rect($startX, $newY, $width, $remainingContentHeight, 'D');
            $pdf->SetXY($startX + 2, $newY + 2);
            $pdf->MultiCell($width - 4, 4, '', 0, $align, false, 1, '', '', true, 0, false, true, $remainingContentHeight - 4, 'T');

        } else {
            // Content fits on current page
            $pdf->Rect($startX, $startY, $width, $actualHeight, 'D');
            $pdf->SetXY($startX + 2, $startY + 2);
            $pdf->MultiCell($width - 4, 4, $text, 0, $align, false, 1, '', '', true, 0, false, true, $actualHeight - 4, 'T');
        }
    }

    /**
     * Generate rating criteria table with proper page break handling
     */
    private function generateRatingTable($pdf, $pageApplicants, $rateItems, $startIndex)
    {
        if (empty($rateItems)) {
            return;
        }

        $pdf->Ln(10);

        // Check if we have space for the rating table header
        $currentY = $pdf->GetY();
        $pageHeight = $pdf->getPageHeight();
        $bottomMargin = 25;
        $availableSpace = $pageHeight - $currentY - $bottomMargin;
        $headerHeight = 20; // Title + table header

        if ($availableSpace < $headerHeight + 20) {
            $pdf->AddPage();
        }

        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Rating Criteria and Scores', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 8);

        // Calculate table width
        $contentWidth = $pdf->getPageWidth() - 20;
        $col1Width = 60;  // Criteria
        $colOutWidth = 20; // Out of
        $remainingWidth = $contentWidth - $col1Width - $colOutWidth;
        $colWidth = $remainingWidth / count($pageApplicants); // Applicant columns

        // Table header
        $this->addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth);

        // Rating rows with page break handling
        $totalAchieved = array_fill(0, count($pageApplicants), 0);
        $totalMax = 0;
        $rowHeight = 8;

        foreach ($rateItems as $rateItem) {
            // Check if row fits on current page
            $currentY = $pdf->GetY();
            $availableSpace = $pageHeight - $currentY - $bottomMargin;

            if ($availableSpace < $rowHeight + 10) {
                $pdf->AddPage();
                // Re-add header on new page
                $this->addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth);
            }

            // Criteria label with text wrapping
            $criteriaText = $rateItem['item_label'];
            $lines = $pdf->getNumLines($criteriaText, $col1Width - 4);
            $actualRowHeight = max($rowHeight, $lines * 4 + 2);

            $startY = $pdf->GetY();
            $startX = $pdf->GetX();

            // Draw criteria cell
            $pdf->MultiCell($col1Width, $actualRowHeight, $criteriaText, 1, 'L', false, 0, '', '', true, 0, false, true, $actualRowHeight, 'M');

            $maxScore = 0;
            $currentX = $startX + $col1Width;

            // Draw score cells
            foreach ($pageApplicants as $index => $applicant) {
                $score = 0;
                $maxScoreForItem = 0;

                // Find rating for this applicant and rate item
                if (!empty($applicant['ratings'])) {
                    foreach ($applicant['ratings'] as $rating) {
                        if ($rating['rate_item_id'] == $rateItem['id']) {
                            $score = $rating['score_achieved'];
                            $maxScoreForItem = $rating['score_max'];
                            break;
                        }
                    }
                }

                $totalAchieved[$index] += $score;
                if ($maxScoreForItem > $maxScore) {
                    $maxScore = $maxScoreForItem;
                }

                $pdf->SetXY($currentX, $startY);
                $pdf->Cell($colWidth, $actualRowHeight, $score, 1, 0, 'C');
                $currentX += $colWidth;
            }

            // Out of column
            $pdf->SetXY($currentX, $startY);
            $pdf->Cell($colOutWidth, $actualRowHeight, $maxScore, 1, 0, 'C');
            $totalMax += $maxScore;

            // Move to next row
            $pdf->SetXY($startX, $startY + $actualRowHeight);
        }

        // Total row
        $currentY = $pdf->GetY();
        $availableSpace = $pageHeight - $currentY - $bottomMargin;

        if ($availableSpace < $rowHeight + 5) {
            $pdf->AddPage();
            $this->addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth);
        }

        $pdf->SetFillColor(220, 220, 220);
        $pdf->SetFont('helvetica', 'B', 8);
        $pdf->Cell($col1Width, $rowHeight, 'Total', 1, 0, 'L', true);
        foreach ($totalAchieved as $total) {
            $pdf->Cell($colWidth, $rowHeight, $total, 1, 0, 'C', true);
        }
        $pdf->Cell($colOutWidth, $rowHeight, $totalMax, 1, 1, 'C', true);
    }

    /**
     * Add rating table header
     */
    private function addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth)
    {
        $pdf->SetFillColor(240, 240, 240);
        $pdf->SetFont('helvetica', 'B', 8);
        $pdf->Cell($col1Width, 8, 'Criteria', 1, 0, 'C', true);
        for ($i = 0; $i < count($pageApplicants); $i++) {
            $pdf->Cell($colWidth, 8, 'Applicant ' . ($startIndex + $i + 1), 1, 0, 'C', true);
        }
        $pdf->Cell($colOutWidth, 8, 'Out of', 1, 1, 'C', true);
    }
}
