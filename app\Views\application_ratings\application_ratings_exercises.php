<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Application Rating</h1>
            <p class="text-muted">Select an exercise to start rating applications</p>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item active">Rating</li>
            </ol>
        </nav>
    </div>

    <!-- Exercises List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-tasks"></i> Select Exercise
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($exercises)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Exercises Available</h5>
                    <p class="text-muted">There are no published exercises available for rating at this time.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">#</th>
                                <th width="30%">Exercise Name</th>
                                <th width="15%">Advertisement No.</th>
                                <th width="15%">Status</th>
                                <th width="15%">Created Date</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($exercises as $exercise): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td>
                                        <strong><?= esc($exercise['exercise_name']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= esc($exercise['advertisement_no']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success"><?= esc(ucfirst($exercise['status'])) ?></span>
                                    </td>
                                    <td>
                                        <small><?= date('M d, Y', strtotime($exercise['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('rating/position-groups/' . $exercise['id']) ?>"
                                           class="btn btn-sm btn-outline-primary" title="Rate Applications">
                                            <i class="fas fa-star"></i> Rate Applications
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Help Section -->
    <div class="card mt-4">
        <div class="card-body">
            <h6 class="card-title">
                <i class="fas fa-info-circle text-info"></i> How to Rate Applications
            </h6>
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-tasks fa-2x text-primary mb-2"></i>
                        <h6>1. Select Exercise</h6>
                        <small class="text-muted">Choose the exercise you want to rate applications for</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-layer-group fa-2x text-success mb-2"></i>
                        <h6>2. Choose Position Group</h6>
                        <small class="text-muted">Select the position group within the exercise</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-briefcase fa-2x text-warning mb-2"></i>
                        <h6>3. Select Position</h6>
                        <small class="text-muted">Pick the specific position to rate applications for</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-star fa-2x text-info mb-2"></i>
                        <h6>4. Rate Applications</h6>
                        <small class="text-muted">Review and rate individual applications</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
