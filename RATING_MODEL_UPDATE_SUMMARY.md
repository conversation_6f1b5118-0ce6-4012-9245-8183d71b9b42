# Application Rating Model Update Summary

## Overview
Updated the `AppxApplicationRatingModel` to match the actual database table structure and added comprehensive field documentation.

## Database Table Structure
```sql
CREATE TABLE `appx_application_rating` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `application_id` int(11) NOT NULL,
 `rate_item_id` int(11) NOT NULL,
 `score_achieved` int(11) NOT NULL COMMENT 'score achieved by the application',
 `score_max` int(11) NOT NULL COMMENT 'max score for this item',
 `justification` text NOT NULL COMMENT 'justification of the score gained.',
 `created_by` int(11) unsigned DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) unsigned DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_application_id` (`application_id`),
 <PERSON><PERSON>Y `idx_rate_item_id` (`rate_item_id`),
 <PERSON><PERSON>Y `idx_created_by` (`created_by`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

## Changes Made

### 1. Model Field Updates (`app/Models/AppxApplicationRatingModel.php`)

#### Field Name Changes:
- `score_grained` → `score_achieved` (score achieved by the application)
- `score_set` → `score_max` (maximum possible score for this item)
- Added `justification` field (text justification for the score)

#### Updated allowedFields:
```php
protected $allowedFields = [
    'application_id',    // Foreign key to appx_application_details
    'rate_item_id',      // Foreign key to rate_items
    'score_achieved',    // Score achieved by the application
    'score_max',         // Maximum score for this item
    'justification',     // Justification of the score gained
    'created_by',        // User who created the rating
    'updated_by',        // User who last updated the rating
    'deleted_by'         // User who soft-deleted the rating
];
```

#### Updated Validation Rules:
```php
protected $validationRules = [
    'application_id'  => 'required|numeric',
    'rate_item_id'    => 'required|numeric',
    'score_achieved'  => 'required|numeric',
    'score_max'       => 'required|numeric',
    'justification'   => 'required|string'
];
```

### 2. Method Updates

#### Updated `updateOrCreateRating()` method:
- Changed signature to include `justification` parameter
- Updated to use new field names
- Added proper parameter documentation

#### Updated query methods:
- `getTotalScoreByApplication()` - uses `score_achieved` and `score_max`
- `getAverageScoresByRateItem()` - uses new field names
- `getRatingStatistics()` - updated for new field names
- `getTopRatedApplications()` - uses `score_achieved`

#### Added new methods:
- `getRatingsWithJustificationsByApplicationId()` - gets ratings with justifications
- `getRatingSummary()` - comprehensive rating summary with percentages

### 3. Controller Updates (`app/Controllers/ApplicationRatingsController.php`)

#### Updated `submitRating()` method:
- Modified to get maximum score from rate items
- Added justification handling
- Updated method call to use new signature:
```php
$this->ratingModel->updateOrCreateRating(
    $applicationId,
    $rateItemId,
    (int)$score,           // score_achieved
    $maxScore,             // score_max
    $justification,        // justification
    session()->get('user_id')
);
```

### 4. View Updates (`app/Views/application_ratings/application_ratings_form.php`)

#### Updated field reference:
- Changed `$rating['score_set']` to `$rating['score_achieved']` for existing rating display

### 5. Documentation Improvements

#### Added comprehensive field documentation:
```php
/**
 * Database Table Structure:
 * - id: Primary key (auto increment)
 * - application_id: Foreign key to appx_application_details table
 * - rate_item_id: Foreign key to rate_items table
 * - score_achieved: Score achieved by the application for this rating item
 * - score_max: Maximum possible score for this rating item
 * - justification: Text justification/reasoning for the score given
 * - created_by: User ID who created this rating
 * - created_at: Timestamp when rating was created
 * - updated_by: User ID who last updated this rating
 * - updated_at: Timestamp when rating was last updated
 * - deleted_by: User ID who soft-deleted this rating
 * - deleted_at: Timestamp when rating was soft-deleted
 */
```

## Important Field Distinctions

### Rating Fields in Two Tables:

#### `appx_application_rating` table:
- `justification` - Specific justification for each individual rating item score
- Stores individual ratings for each rating criteria

#### `appx_application_details` table:
- `rating_remarks` - Overall remarks/comments about the entire application rating
- `rating_capability_max` - Total score achieved across all rating items
- `rating_status` - Overall rating status (pending, completed, etc.)
- `rated_by` - User who completed the rating
- `rated_at` - When the rating was completed

### Usage:
- **Individual item justifications** go in `appx_application_rating.justification`
- **Overall rating comments** go in `appx_application_details.rating_remarks`

## Benefits of Updates

1. **Database Consistency**: Model now matches actual database schema
2. **Better Documentation**: Clear field descriptions and comments
3. **Enhanced Functionality**: Added justification support for individual rating items
4. **Improved Validation**: Proper validation rules for all fields
5. **Better Methods**: New helper methods for rating summaries and analysis
6. **Clear Field Separation**: Distinct handling of item-specific vs. overall rating data

## Testing

A test script (`test_rating_model.php`) has been created to verify:
- Model configuration
- Database connectivity
- Field existence
- Method functionality
- Validation rules

## Files Modified

1. `app/Models/AppxApplicationRatingModel.php` - Complete model update
2. `app/Controllers/ApplicationRatingsController.php` - Method signature updates
3. `app/Views/application_ratings/application_ratings_form.php` - Field reference update

## Next Steps

1. Test the rating functionality to ensure everything works correctly
2. Consider adding more detailed justification fields if needed
3. Update any reports or analytics that use the old field names
4. Consider adding database migration script if needed for existing data
