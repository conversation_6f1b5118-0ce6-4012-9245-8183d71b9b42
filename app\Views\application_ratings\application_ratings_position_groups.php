<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Application Rating</h1>
            <p class="text-muted">Select a position group to continue</p>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>">Rating</a></li>
                <li class="breadcrumb-item active">Position Groups</li>
            </ol>
        </nav>
    </div>

    <!-- Exercise Info -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-1"><?= esc($exercise['exercise_name']) ?></h5>
                    <p class="text-muted mb-0">
                        <strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?> | 
                        <strong>Organization:</strong> <?= esc($exercise['org_name']) ?>
                    </p>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('rating') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Exercises
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Groups List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-layer-group"></i> Select Position Group
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($positionGroups)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Position Groups Found</h5>
                    <p class="text-muted">There are no position groups available for this exercise.</p>
                    <a href="<?= base_url('rating') ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Exercises
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Position Group Name</th>
                                <th>Number of Positions</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positionGroups as $group): ?>
                                <tr>
                                    <td>
                                        <strong><?= esc($group['group_name']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $group['position_count'] ?> Position<?= $group['position_count'] != 1 ? 's' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('rating/positions/' . $group['id']) ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-arrow-right"></i> View Positions
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="card mt-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="mb-1">
                        <i class="fas fa-route text-info"></i> Navigation Path
                    </h6>
                    <p class="text-muted mb-0">
                        Dashboard → Rating → <strong>Position Groups</strong> → Positions → Applications
                    </p>
                </div>
                <div class="col-auto">
                    <small class="text-muted">Step 2 of 4</small>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
