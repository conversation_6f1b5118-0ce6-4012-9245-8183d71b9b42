<?php

/**
 * Test script to verify AppxApplicationRatingModel functionality
 * Run this from the command line or browser to test the updated model
 */

// Include CodeIgniter bootstrap
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Initialize CodeIgniter
$app = new \CodeIgniter\CodeIgniter(new \Config\App());
$app->initialize();

// Load the model
$ratingModel = new \App\Models\AppxApplicationRatingModel();

echo "<h2>Testing AppxApplicationRatingModel</h2>\n";

// Test 1: Check table structure
echo "<h3>1. Testing Model Configuration</h3>\n";
echo "Table: " . $ratingModel->getTable() . "\n";
echo "Primary Key: " . $ratingModel->getPrimaryKey() . "\n";
echo "Allowed Fields: " . implode(', ', $ratingModel->getAllowedFields()) . "\n";

// Test 2: Test validation rules
echo "<h3>2. Testing Validation Rules</h3>\n";
$validationRules = $ratingModel->getValidationRules();
foreach ($validationRules as $field => $rules) {
    echo "Field: {$field} - Rules: {$rules}\n";
}

// Test 3: Test database connection and basic query
echo "<h3>3. Testing Database Connection</h3>\n";
try {
    $count = $ratingModel->countAllResults(false);
    echo "Total ratings in database: {$count}\n";
    
    // Get a sample rating if any exist
    if ($count > 0) {
        $sampleRating = $ratingModel->first();
        echo "Sample rating fields: " . implode(', ', array_keys($sampleRating)) . "\n";
        
        // Check if the new fields exist
        $expectedFields = ['score_achieved', 'score_max', 'justification'];
        foreach ($expectedFields as $field) {
            if (array_key_exists($field, $sampleRating)) {
                echo "✓ Field '{$field}' exists\n";
            } else {
                echo "✗ Field '{$field}' missing\n";
            }
        }
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

// Test 4: Test new methods
echo "<h3>4. Testing New Methods</h3>\n";
try {
    // Test getRatingSummary method
    if ($count > 0) {
        $sampleRating = $ratingModel->first();
        $applicationId = $sampleRating['application_id'];
        
        $summary = $ratingModel->getRatingSummary($applicationId);
        echo "Rating summary for application {$applicationId}:\n";
        echo "- Total achieved: " . ($summary['total_achieved'] ?? 'N/A') . "\n";
        echo "- Total max: " . ($summary['total_max'] ?? 'N/A') . "\n";
        echo "- Percentage: " . ($summary['percentage'] ?? 'N/A') . "%\n";
        echo "- Number of ratings: " . count($summary['ratings'] ?? []) . "\n";
    }
} catch (Exception $e) {
    echo "Method test error: " . $e->getMessage() . "\n";
}

echo "<h3>5. Model Update Summary</h3>\n";
echo "✓ Updated field names to match database schema:\n";
echo "  - score_grained → score_achieved\n";
echo "  - score_set → score_max\n";
echo "  - Added justification field\n";
echo "✓ Updated validation rules\n";
echo "✓ Updated all methods to use new field names\n";
echo "✓ Added comprehensive field documentation\n";
echo "✓ Added new helper methods for rating summaries\n";

echo "\n<p><strong>Model is ready for use with the updated database schema!</strong></p>\n";
