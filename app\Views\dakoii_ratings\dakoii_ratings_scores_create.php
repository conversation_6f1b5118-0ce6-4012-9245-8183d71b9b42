<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Add Rating Score for "<?= esc($item['item_label']) ?>"</h5>
        <a href="<?= base_url('dakoii/rating_items/edit/' . $item['id']) ?>" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to Item
        </a>
    </div>
    <div class="card-body">
        <form action="<?= base_url('dakoii/rating_scores/create') ?>" method="post">
            <?= csrf_field() ?>
            
            <input type="hidden" name="item_id" value="<?= $item['id'] ?>">
            
            <div class="mb-3">
                <label for="score" class="form-label">Score Value</label>
                <input type="number" step="0.01" class="form-control" id="score" name="score" value="<?= old('score') ?>" required>
                <?php if (session()->has('error') && is_array(session('error')) && isset(session('error')['score'])) : ?>
                    <div class="text-danger mt-2"><?= session('error')['score'] ?></div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label for="label" class="form-label">Label</label>
                <input type="text" class="form-control" id="label" name="label" value="<?= old('label') ?>" maxlength="100" required>
                <?php if (session()->has('error') && is_array(session('error')) && isset(session('error')['label'])) : ?>
                    <div class="text-danger mt-2"><?= session('error')['label'] ?></div>
                <?php endif; ?>
                <div class="form-text">Provide a label for this score (e.g., "Excellent", "Good", "Poor").</div>
            </div>

            <div class="mb-3">
                <label for="score_description" class="form-label">Score Description</label>
                <textarea class="form-control" id="score_description" name="score_description" rows="3"><?= old('score_description') ?></textarea>
                <?php if (session()->has('error') && is_array(session('error')) && isset(session('error')['score_description'])) : ?>
                    <div class="text-danger mt-2"><?= session('error')['score_description'] ?></div>
                <?php endif; ?>
                <div class="form-text">Provide a description of what this score represents (optional).</div>
            </div>
            
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Score
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?> 