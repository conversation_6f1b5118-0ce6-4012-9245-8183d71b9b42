# DERS Technical Stack

## Framework & Core Technologies
- **Framework**: CodeIgniter 4 (PHP MVC framework)
- **PHP Version**: 8.1+ (minimum requirement)
- **Database**: MySQL/MariaDB (ders_db schema)
- **Web Server**: Apache/Nginx with mod_rewrite
- **Frontend**: Bootstrap-based responsive design
- **Authentication**: Session-based with role-based access control

## Key Dependencies
```json
{
  "php": "^8.1",
  "codeigniter4/framework": "^4.0",
  "setasign/fpdf": "^1.8",
  "setasign/fpdi": "^2.6", 
  "smalot/pdfparser": "^2.12"
}
```

## Development Dependencies
- **Autoloading**: PSR-4 compliant

## File Processing Capabilities
- PDF generation and manipulation (FPDF/FPDI)
- PDF text extraction (smalot/pdfparser)
- Document upload and management
- Background processing for file extraction

## Common Commands

### Development Setup
```bash
composer install
cp env .env
# Configure database settings in .env
```



### Database Operations
- Use CodeIgniter migrations in `app/Database/Migrations/`
- Database backups stored in `db_backups/`
- Use Spark CLI for database operations

### File Structure
- Entry point: `public/index.php` (not root index.php)
- Application code: `app/` directory
- Public assets: `public/assets/`
- File uploads: `public/uploads/` and `writable/uploads/`

## Environment Configuration
- Copy `env` to `.env` for local configuration
- Configure `baseURL` and database settings
- Set appropriate file upload limits for document processing