<?php
/**
 * View file for Form 3.7 Profiling Report - Positions List
 *
 * @var array $exercise Exercise details
 * @var array $position_group Position group details
 * @var array $positions List of positions with application counts
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/form37/' . $exercise['id']) ?>">Form 3.7 Profiling</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position_group['group_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Form 3.7 Profiling - Positions</h2>
                    <p class="text-muted mb-0"><?= esc($position_group['group_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/form37/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Position Groups
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        Select Position for Form 3.7 Report
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Positions Found</h5>
                            <p class="text-muted">There are no positions available in this position group.</p>
                            <a href="<?= base_url('reports/form37/' . $exercise['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> Back to Position Groups
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="positionsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="25%">Position Details</th>
                                        <th width="15%">Organization</th>
                                        <th width="15%">Classification & Award</th>
                                        <th width="10%">Applications</th>
                                        <th width="15%">Location</th>
                                        <th width="15%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($positions as $position): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($position['designation']) ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        Ref: <?= esc($position['position_reference']) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= esc($position['org_name'] ?? 'N/A') ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="d-block">
                                                        <strong>Class:</strong> <?= esc($position['classification']) ?>
                                                    </small>
                                                    <small class="d-block">
                                                        <strong>Award:</strong> <?= esc($position['award']) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success fs-6">
                                                    <?= $position['application_count'] ?? 0 ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-info">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?= esc($position['location']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('reports/form37/positions/' . $position['id']) ?>" 
                                                   class="btn btn-success btn-sm">
                                                    <i class="fas fa-file-alt me-1"></i>
                                                    View Form 3.7
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Group Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Group Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Group Name:</dt>
                                <dd class="col-sm-8"><?= esc($position_group['group_name']) ?></dd>
                                <dt class="col-sm-4">Exercise:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['exercise_name']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Description:</dt>
                                <dd class="col-sm-8"><?= esc($position_group['description'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Total Positions:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-info"><?= count($positions) ?></span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']], // Sort by position name
        columnDefs: [
            { orderable: false, targets: [6] } // Disable sorting for Actions column
        ]
    });
});
</script>
<?= $this->endSection() ?>
