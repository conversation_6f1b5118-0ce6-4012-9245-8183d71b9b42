<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\PositionsGroupModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\AppxApplicationRatingModel;

/**
 * ReportsScoringController
 * 
 * Controller for scoring reports with navigation flow:
 * Exercise -> Position Groups -> Positions -> Applications -> Scoring Details
 */
class ReportsScoringController extends Controller
{
    protected $exerciseModel;
    protected $positionGroupModel;
    protected $positionModel;
    protected $applicationModel;
    protected $ratingModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionsModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->ratingModel = new AppxApplicationRatingModel();
    }

    /**
     * [GET] Scoring Report - List Position Groups for Exercise
     * URI: /reports/scoring/{exerciseId}
     */
    public function scoring($exerciseId)
    {
        // Get exercise data
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get position groups with position counts for this exercise
        $positionGroups = $this->positionGroupModel->getPositionGroupsWithCountByExerciseId($exerciseId);

        $data = [
            'title' => 'Scoring Report - ' . $exercise['exercise_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'position_groups' => $positionGroups
        ];

        return view('application_reports/appx_reports_scoring_groups', $data);
    }

    /**
     * [GET] List Positions in Position Group
     * URI: /reports/scoring/groups/{groupId}
     */
    public function groups($groupId)
    {
        // Get position group data
        $positionGroup = $this->positionGroupModel->find($groupId);
        if (!$positionGroup) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position group not found');
        }

        // Get exercise data
        $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get positions with application counts for this group
        $positions = $this->positionModel->getPositionsByGroupIdWithApplicationCount($groupId);

        $data = [
            'title' => 'Scoring Report - Positions in ' . $positionGroup['group_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'position_group' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_reports/appx_reports_scoring_positions', $data);
    }

    /**
     * [GET] List Applications with Scores for Position
     * URI: /reports/scoring/positions/{positionId}
     */
    public function positions($positionId)
    {
        // Get position data with related information
        $position = $this->positionModel->select('
                positions.*,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->find($positionId);

        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Get applications for this position
        $applications = $this->applicationModel->where('position_id', $positionId)->findAll();

        // Add scoring data to each application
        foreach ($applications as &$application) {
            $totals = $this->ratingModel->getTotalScoreByApplication($application['id']);
            $application['total_score'] = $totals['total_achieved'] ?? 0;
            $application['max_score'] = $totals['total_max'] ?? 0;
            $application['percentage'] = $totals['total_max'] > 0 ? 
                round(($totals['total_achieved'] / $totals['total_max']) * 100, 2) : 0;
        }

        // Sort by percentage descending
        usort($applications, function($a, $b) {
            return $b['percentage'] <=> $a['percentage'];
        });

        $data = [
            'title' => 'Scoring Report - Applications for ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'applications' => $applications
        ];

        return view('application_reports/appx_reports_scoring_applications', $data);
    }

    /**
     * [GET] Detailed Scoring View for Application
     * URI: /reports/scoring/applications/{applicationId}
     */
    public function applications($applicationId)
    {
        // Get application data with position and exercise details
        $application = $this->applicationModel->select('
                appx_application_details.*,
                positions.designation,
                positions.position_reference,
                positions.position_group_id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                dakoii_org.org_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->find($applicationId);

        if (!$application) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Application not found');
        }

        // Get detailed rating summary for this application
        $ratingSummary = $this->ratingModel->getRatingSummary($applicationId);

        $data = [
            'title' => 'Scoring Details - ' . $application['first_name'] . ' ' . $application['last_name'],
            'menu' => 'reports',
            'application' => $application,
            'rating_summary' => $ratingSummary
        ];

        return view('application_reports/appx_reports_scoring_detail', $data);
    }

    /**
     * [POST] Export Scoring Detail Report to PDF
     * URI: /reports/scoring/applications/export
     */
    public function exportScoringDetail()
    {
        // Get and validate parameters
        $applicationId = $this->request->getPost('application_id');
        if (!$applicationId) {
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Application ID is required'
            ]);
        }

        try {
            // Get application data with position and exercise details
            $application = $this->applicationModel->select('
                    appx_application_details.*,
                    positions.designation,
                    positions.position_reference,
                    positions.position_group_id,
                    positions_groups.group_name,
                    positions_groups.exercise_id,
                    exercises.exercise_name,
                    dakoii_org.org_name
                ')
                ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
                ->find($applicationId);

            if (!$application) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Application not found'
                ]);
            }

            // Get detailed rating summary for this application
            $ratingSummary = $this->ratingModel->getRatingSummary($applicationId);

            // Generate and output PDF directly to browser
            $this->generateScoringDetailPDF($application, $ratingSummary);

        } catch (\Exception $e) {
            log_message('error', 'Scoring Detail PDF Export Error: ' . $e->getMessage());
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * [POST] Export Applications Scoring Report to PDF
     * URI: /reports/scoring/positions/export
     */
    public function exportApplicationsScoring()
    {
        // Get and validate parameters
        $positionId = $this->request->getPost('position_id');
        if (!$positionId) {
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Position ID is required'
            ]);
        }

        try {
            // Get position data with related information
            $position = $this->positionModel->select('
                    positions.*,
                    positions_groups.group_name,
                    positions_groups.exercise_id,
                    exercises.exercise_name,
                    dakoii_org.org_name
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->find($positionId);

            if (!$position) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Position not found'
                ]);
            }

            // Get applications for this position
            $applications = $this->applicationModel->where('position_id', $positionId)->findAll();

            // Add scoring data to each application
            foreach ($applications as &$application) {
                $totals = $this->ratingModel->getTotalScoreByApplication($application['id']);
                $application['total_score'] = $totals['total_achieved'] ?? 0;
                $application['max_score'] = $totals['total_max'] ?? 0;
                $application['percentage'] = $totals['total_max'] > 0 ?
                    round(($totals['total_achieved'] / $totals['total_max']) * 100, 2) : 0;
            }

            // Sort by percentage descending
            usort($applications, function($a, $b) {
                return $b['percentage'] <=> $a['percentage'];
            });

            // Generate and output PDF directly to browser
            $this->generateApplicationsScoringPDF($position, $applications);

        } catch (\Exception $e) {
            log_message('error', 'Applications Scoring PDF Export Error: ' . $e->getMessage());
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate Scoring Detail PDF
     *
     * @param array $application Application data
     * @param array $ratingSummary Rating summary data
     */
    private function generateScoringDetailPDF($application, $ratingSummary)
    {
        try {
            // Create TCPDF with custom footer (Landscape orientation)
            $pdf = new class('L', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    $this->SetY(-18);
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = $this->getPageWidth() - 30; // Account for left and right margins (15mm each)

                    // Draw PNG flag colored sections
                    $this->SetFillColor(240, 15, 0); // Red
                    $this->Rect(15, $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(0, 0, 0); // Black
                    $this->Rect(15 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(255, 194, 15); // Gold
                    $this->Rect(15 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Add content
                    $this->SetFont('helvetica', '', 8);
                    $this->SetY($footerY + 1);

                    // Row 1
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(17);
                    $this->Cell($footerWidth / 3, 3, 'Generated by DERS System v1.0', 0, 0, 'L');

                    $this->SetX(15 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                    $this->SetX(15 + (2 * $footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . ' of ' . $this->getAliasNbPages(), 0, 0, 'C');

                    // Row 2
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(17);
                    $this->Cell($footerWidth / 3, 3, 'AI-Powered', 0, 0, 'L');

                    $this->SetX(15 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Row 3
                    $this->SetY($this->GetY() + 3);
                    $this->SetX(15);
                    $this->Cell($footerWidth, 3, 'ders.dakoiims.com', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Configure PDF
            $pdf->SetCreator('DERS System');
            $pdf->SetTitle('Scoring Detail Report - ' . $application['first_name'] . ' ' . $application['last_name']);
            $pdf->SetMargins(15, 20, 15);
            $pdf->SetAutoPageBreak(true, 25);
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);

            // Add page
            $pdf->AddPage();

            // Add logo
            $logoPath = FCPATH . 'public/assets/system_img/system-logo.png';
            if (file_exists($logoPath)) {
                $pdf->Image($logoPath, ($pdf->getPageWidth() - 20) / 2, $pdf->GetY(), 20, 20);
                $pdf->Ln(25);
            }

            // Add title
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, 'Scoring Detail Report', 0, 1, 'C');
            $pdf->Ln(5);

            // Add content
            $this->addScoringDetailContent($pdf, $application, $ratingSummary);

            // Generate filename and output directly to browser
            $filename = 'scoring_detail_' . $application['application_number'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output($filename, 'D');

        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate Applications Scoring PDF
     *
     * @param array $position Position data
     * @param array $applications Applications data with scores
     */
    private function generateApplicationsScoringPDF($position, $applications)
    {
        try {
            // Create TCPDF with custom footer (Landscape orientation)
            $pdf = new class('L', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    $this->SetY(-18);
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = $this->getPageWidth() - 30; // Account for left and right margins (15mm each)

                    // Draw PNG flag colored sections
                    $this->SetFillColor(240, 15, 0); // Red
                    $this->Rect(15, $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(0, 0, 0); // Black
                    $this->Rect(15 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(255, 194, 15); // Gold
                    $this->Rect(15 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Add content
                    $this->SetFont('helvetica', '', 8);
                    $this->SetY($footerY + 1);

                    // Row 1
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(17);
                    $this->Cell($footerWidth / 3, 3, 'Generated by DERS System v1.0', 0, 0, 'L');

                    $this->SetX(15 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                    $this->SetX(15 + (2 * $footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . ' of ' . $this->getAliasNbPages(), 0, 0, 'C');

                    // Row 2
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(17);
                    $this->Cell($footerWidth / 3, 3, 'AI-Powered', 0, 0, 'L');

                    $this->SetX(15 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Row 3
                    $this->SetY($this->GetY() + 3);
                    $this->SetX(15);
                    $this->Cell($footerWidth, 3, 'ders.dakoiims.com', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Configure PDF
            $pdf->SetCreator('DERS System');
            $pdf->SetTitle('Applications Scoring Report - ' . $position['designation']);
            $pdf->SetMargins(15, 20, 15);
            $pdf->SetAutoPageBreak(true, 25);
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);

            // Add page
            $pdf->AddPage();

            // Add logo
            $logoPath = FCPATH . 'public/assets/system_img/system-logo.png';
            if (file_exists($logoPath)) {
                $pdf->Image($logoPath, ($pdf->getPageWidth() - 20) / 2, $pdf->GetY(), 20, 20);
                $pdf->Ln(25);
            }

            // Add title
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, 'Applications Scoring Report', 0, 1, 'C');
            $pdf->Ln(5);

            // Add content
            $this->addApplicationsScoringContent($pdf, $position, $applications);

            // Generate filename and output directly to browser
            $filename = 'applications_scoring_' . $position['position_reference'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output($filename, 'D');

        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add scoring detail content to PDF
     *
     * @param object $pdf TCPDF instance
     * @param array $application Application data
     * @param array $ratingSummary Rating summary data
     */
    private function addScoringDetailContent($pdf, $application, $ratingSummary)
    {
        // Applicant Information
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Applicant Information', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        // Use two-column layout for better space utilization in landscape
        $leftColWidth = ($pdf->getPageWidth() - 30) / 2 - 5; // Half width minus margin
        $rightColWidth = ($pdf->getPageWidth() - 30) / 2 - 5;

        // Left column
        $startY = $pdf->GetY();
        $pdf->Cell(50, 6, 'Full Name:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $application['first_name'] . ' ' . $application['last_name'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Application No:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $application['application_number'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Email:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $application['email_address'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Gender:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $application['gender'], 0, 1, 'L');

        // Right column
        $pdf->SetXY(15 + $leftColWidth + 10, $startY);

        $pdf->Cell(60, 6, 'Position:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 60, 6, $application['designation'], 0, 1, 'L');

        $pdf->SetX(15 + $leftColWidth + 10);
        $pdf->Cell(60, 6, 'Position Reference:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 60, 6, $application['position_reference'], 0, 1, 'L');

        $pdf->SetX(15 + $leftColWidth + 10);
        $pdf->Cell(60, 6, 'Position Group:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 60, 6, $application['group_name'], 0, 1, 'L');

        $pdf->SetX(15 + $leftColWidth + 10);
        $pdf->Cell(60, 6, 'Exercise:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 60, 6, $application['exercise_name'], 0, 1, 'L');

        $pdf->SetX(15 + $leftColWidth + 10);
        $pdf->Cell(60, 6, 'Organization:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 60, 6, $application['org_name'], 0, 1, 'L');

        // Reset to full width and add spacing
        $pdf->SetX(15);
        $pdf->Ln(8);

        // Scoring Summary
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Scoring Summary', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        $pdf->Cell(50, 6, 'Total Score:', 0, 0, 'L');
        $pdf->Cell(0, 6, number_format($ratingSummary['total_achieved'], 1) . ' / ' . number_format($ratingSummary['total_max'], 1), 0, 1, 'L');

        $pdf->Cell(50, 6, 'Percentage:', 0, 0, 'L');
        $pdf->Cell(0, 6, number_format($ratingSummary['percentage'], 1) . '%', 0, 1, 'L');

        $pdf->Ln(5);

        // Detailed Scoring Breakdown
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Detailed Scoring Breakdown', 0, 1, 'L');

        if (!empty($ratingSummary['ratings'])) {
            $pdf->SetFont('helvetica', '', 9);

            // Calculate table width to fit full content width (landscape A4 content width ≈ 267mm)
            $contentWidth = $pdf->getPageWidth() - 30; // Account for margins
            $col1Width = 15;  // # column
            $col2Width = 80;  // Rating Criteria (wider for landscape)
            $col3Width = 30;  // Score Achieved
            $col4Width = 30;  // Maximum Score
            $col5Width = 25;  // Percentage
            $col6Width = $contentWidth - ($col1Width + $col2Width + $col3Width + $col4Width + $col5Width); // Justification (remaining width)

            // Table header
            $pdf->SetFillColor(240, 240, 240);
            $pdf->Cell($col1Width, 8, '#', 1, 0, 'C', true);
            $pdf->Cell($col2Width, 8, 'Rating Criteria', 1, 0, 'L', true);
            $pdf->Cell($col3Width, 8, 'Score Achieved', 1, 0, 'C', true);
            $pdf->Cell($col4Width, 8, 'Maximum Score', 1, 0, 'C', true);
            $pdf->Cell($col5Width, 8, 'Percentage', 1, 0, 'C', true);
            $pdf->Cell($col6Width, 8, 'Justification', 1, 1, 'L', true);

            $count = 1;
            foreach ($ratingSummary['ratings'] as $rating) {
                $itemPercentage = $rating['score_max'] > 0 ?
                    round(($rating['score_achieved'] / $rating['score_max']) * 100, 1) : 0;

                // Calculate row height based on justification text
                $justificationText = $rating['justification'] ?? 'No remarks provided';
                $lines = $pdf->getNumLines($justificationText, $col6Width);
                $rowHeight = max(8, $lines * 4 + 2);

                $pdf->Cell($col1Width, $rowHeight, $count++, 1, 0, 'C');
                $pdf->Cell($col2Width, $rowHeight, $rating['item_label'] ?? 'N/A', 1, 0, 'L');
                $pdf->Cell($col3Width, $rowHeight, number_format($rating['score_achieved'], 1), 1, 0, 'C');
                $pdf->Cell($col4Width, $rowHeight, number_format($rating['score_max'], 1), 1, 0, 'C');
                $pdf->Cell($col5Width, $rowHeight, $itemPercentage . '%', 1, 0, 'C');

                // Use MultiCell for justification with proper positioning
                $currentY = $pdf->GetY();
                $currentX = $pdf->GetX();
                $pdf->MultiCell($col6Width, $rowHeight, $justificationText, 1, 'L', false, 1, $currentX, $currentY);
            }

            // Total row
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->SetFillColor(220, 220, 220);
            $pdf->Cell($col1Width + $col2Width, 8, 'Total:', 1, 0, 'R', true);
            $pdf->Cell($col3Width, 8, number_format($ratingSummary['total_achieved'], 1), 1, 0, 'C', true);
            $pdf->Cell($col4Width, 8, number_format($ratingSummary['total_max'], 1), 1, 0, 'C', true);
            $pdf->Cell($col5Width, 8, number_format($ratingSummary['percentage'], 1) . '%', 1, 0, 'C', true);
            $pdf->Cell($col6Width, 8, '', 1, 1, 'C', true);
        } else {
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(0, 8, 'No scoring data available for this application.', 0, 1, 'L');
        }

        $pdf->Ln(10);

        // Overall Rating Remarks
        if (!empty($application['rating_remarks'])) {
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Overall Rating Remarks', 0, 1, 'L');
            $pdf->SetFont('helvetica', '', 10);
            $pdf->MultiCell(0, 6, $application['rating_remarks'], 0, 'L');
        }
    }

    /**
     * Add applications scoring content to PDF
     *
     * @param object $pdf TCPDF instance
     * @param array $position Position data
     * @param array $applications Applications data with scores
     */
    private function addApplicationsScoringContent($pdf, $position, $applications)
    {
        // Position Information
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Position Information', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        // Use two-column layout
        $leftColWidth = ($pdf->getPageWidth() - 30) / 2 - 5;
        $rightColWidth = ($pdf->getPageWidth() - 30) / 2 - 5;

        // Left column
        $startY = $pdf->GetY();
        $pdf->Cell(50, 6, 'Position:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['designation'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Position Reference:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['position_reference'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Classification:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['classification'], 0, 1, 'L');

        // Right column
        $pdf->SetXY(15 + $leftColWidth + 10, $startY);

        $pdf->Cell(50, 6, 'Position Group:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['group_name'], 0, 1, 'L');

        $pdf->SetX(15 + $leftColWidth + 10);
        $pdf->Cell(50, 6, 'Exercise:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['exercise_name'], 0, 1, 'L');

        $pdf->SetX(15 + $leftColWidth + 10);
        $pdf->Cell(50, 6, 'Organization:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['org_name'], 0, 1, 'L');

        // Reset to full width and add spacing
        $pdf->SetX(15);
        $pdf->Ln(8);

        // Applications Summary
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Applications Summary', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        $pdf->Cell(50, 6, 'Total Applications:', 0, 0, 'L');
        $pdf->Cell(0, 6, count($applications), 0, 1, 'L');

        $pdf->Ln(5);

        // Applications Table
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Applications with Scoring', 0, 1, 'L');

        if (!empty($applications)) {
            $pdf->SetFont('helvetica', '', 9);

            // Calculate table width to fit full content width
            $contentWidth = $pdf->getPageWidth() - 30;
            $col1Width = 20;  // Rank
            $col2Width = 50;  // Application No
            $col3Width = 100; // Applicant Name (wider since we removed gender and contact)
            $col4Width = 40;  // Total Score
            $col5Width = $contentWidth - ($col1Width + $col2Width + $col3Width + $col4Width); // Percentage (remaining width)

            // Table header
            $pdf->SetFillColor(240, 240, 240);
            $pdf->Cell($col1Width, 8, 'Rank', 1, 0, 'C', true);
            $pdf->Cell($col2Width, 8, 'Application No.', 1, 0, 'C', true);
            $pdf->Cell($col3Width, 8, 'Applicant Name', 1, 0, 'L', true);
            $pdf->Cell($col4Width, 8, 'Total Score', 1, 0, 'C', true);
            $pdf->Cell($col5Width, 8, 'Percentage', 1, 1, 'C', true);

            $rank = 1;
            foreach ($applications as $application) {
                $pdf->Cell($col1Width, 8, $rank++, 1, 0, 'C');
                $pdf->Cell($col2Width, 8, $application['application_number'], 1, 0, 'L');
                $pdf->Cell($col3Width, 8, $application['first_name'] . ' ' . $application['last_name'], 1, 0, 'L');
                $pdf->Cell($col4Width, 8, number_format($application['total_score'], 1) . '/' . number_format($application['max_score'], 1), 1, 0, 'C');
                $pdf->Cell($col5Width, 8, number_format($application['percentage'], 1) . '%', 1, 1, 'C');
            }
        } else {
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(0, 8, 'No applications found for this position.', 0, 1, 'L');
        }
    }
}
