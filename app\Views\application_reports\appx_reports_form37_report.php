<?php
/**
 * View file for Form 3.7 Profiling Report
 *
 * @var array $position Position details
 * @var array $applications List of applications for profiling
 * @var int $total_applications Total number of applications
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $position['exercise_id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/form37/' . $position['exercise_id']) ?>">Form 3.7 Profiling</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/form37/groups/' . $position['position_group_id']) ?>"><?= esc($position['group_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Form 3.7 Profiling Report</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/form37/groups/' . $position['position_group_id']) ?>" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                    <button type="button" class="btn btn-danger me-2" onclick="exportForm37PDF()">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </button>
                    <button type="button" class="btn btn-success" onclick="printReport()">
                        <i class="fas fa-print me-1"></i>
                        Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php if (empty($applications)): ?>
        <!-- No Applications Found -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Applications Found</h5>
                        <p class="text-muted">There are no applications available for this position.</p>
                        <a href="<?= base_url('reports/form37/groups/' . $position['position_group_id']) ?>" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Positions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Report Content -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm" id="reportContent">
                    <div class="card-body">
                        <!-- Report Header -->
                        <div class="row mb-4 text-center">
                            <div class="col-12">
                                <img src="<?= base_url('public/assets/system_img/system-logo.png') ?>" 
                                     alt="Organization Logo" style="width: 80px;" class="mb-3">
                                <h4 class="mb-1"><?= strtoupper(esc($position['org_name'])) ?></h4>
                                <h5 class="mb-1">APPLICANT PROFILE REPORT</h5>
                                <p class="text-end mb-0"><strong>FORM RS 3.7</strong></p>
                            </div>
                        </div>

                        <!-- Position Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-5">Position Reference:</dt>
                                    <dd class="col-sm-7"><?= esc($position['position_reference']) ?></dd>
                                    <dt class="col-sm-5">Designation:</dt>
                                    <dd class="col-sm-7"><?= esc($position['designation']) ?></dd>
                                    <dt class="col-sm-5">Classification:</dt>
                                    <dd class="col-sm-7"><?= esc($position['classification']) ?></dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-5">Exercise:</dt>
                                    <dd class="col-sm-7"><?= esc($position['exercise_name']) ?></dd>
                                    <dt class="col-sm-5">Position Group:</dt>
                                    <dd class="col-sm-7"><?= esc($position['group_name']) ?></dd>
                                    <dt class="col-sm-5">Total Applicants:</dt>
                                    <dd class="col-sm-7"><strong><?= $total_applications ?></strong></dd>
                                </dl>
                            </div>
                        </div>

                        <?php
                        // Pagination setup - 2 applicants per page
                        $applicantsPerPage = 2;
                        $totalPages = ceil($total_applications / $applicantsPerPage);
                        ?>

                        <!-- Paginated Content -->
                        <?php for ($page = 1; $page <= $totalPages; $page++): ?>
                            <?php
                            $startIndex = ($page - 1) * $applicantsPerPage;
                            $pageApplicants = array_slice($applications, $startIndex, $applicantsPerPage);
                            ?>

                            <div class="page-section" id="page-<?= $page ?>" style="<?= $page > 1 ? 'display: none;' : '' ?>">
                                <!-- Position vs Person Specifications Table -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="mb-3">Comparative Analysis - Page <?= $page ?> of <?= $totalPages ?></h6>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th width="25%">POSITION SPECIFICATION</th>
                                                        <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                            <th width="<?= 75 / count($pageApplicants) ?>%">
                                                                PERSON SPECIFICATION<br>
                                                                <small>Applicant <?= $startIndex + $i + 1 ?></small>
                                                            </th>
                                                        <?php endfor; ?>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Personal Information Row -->
                                                    <tr>
                                                        <td><strong>Personal Information</strong></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td>
                                                                <strong>Full Name:</strong> <?= esc($applicant['profile']['full_name'] ?? 'N/A') ?><br>
                                                                <strong>Sex:</strong> <?= esc($applicant['profile']['sex'] ?? 'N/A') ?><br>
                                                                <strong>Birth Date & Age:</strong> <?= esc($applicant['profile']['bdate_age'] ?? 'N/A') ?><br>
                                                                <strong>Place of Origin:</strong> <?= esc($applicant['profile']['place_origin'] ?? 'N/A') ?><br>
                                                                <strong>Contact Details:</strong> <?= esc($applicant['profile']['contact_details'] ?? 'N/A') ?><br>
                                                                <strong>ID Document Numbers:</strong> <?= esc($applicant['profile']['id_document_numbers'] ?? 'N/A') ?><br>
                                                                <strong>Current Employer:</strong> <?= esc($applicant['profile']['current_employer'] ?? 'N/A') ?><br>
                                                                <strong>Current Position:</strong> <?= esc($applicant['profile']['current_position'] ?? 'N/A') ?><br>
                                                                <strong>Address/Location:</strong> <?= esc($applicant['profile']['address_location'] ?? 'N/A') ?>
                                                            </td>
                                                        <?php endforeach; ?>
                                                    </tr>

                                                    <!-- Qualifications Section -->
                                                    <tr>
                                                        <td><strong>Qualifications</strong></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td class="text-muted">Applicant qualifications</td>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                    <tr>
                                                        <td><?= nl2br(esc($position['qualifications'] ?? 'Not specified')) ?></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td><?= nl2br(esc($applicant['profile']['qualification_text'] ?? 'Not provided')) ?></td>
                                                        <?php endforeach; ?>
                                                    </tr>

                                                    <!-- Other Training Courses Section -->
                                                    <tr>
                                                        <td><strong>Other Training Courses</strong></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td class="text-muted">Additional training attended</td>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                    <tr>
                                                        <td>N/A</td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td><?= nl2br(esc($applicant['profile']['other_trainings'] ?? 'Not provided')) ?></td>
                                                        <?php endforeach; ?>
                                                    </tr>

                                                    <!-- Job Experiences Section -->
                                                    <tr>
                                                        <td><strong>Job Experiences</strong></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td class="text-muted">Applicant job experience</td>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                    <tr>
                                                        <td><?= nl2br(esc($position['job_experiences'] ?? 'Not specified')) ?></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td><?= nl2br(esc($applicant['profile']['job_experiences'] ?? 'Not provided')) ?></td>
                                                        <?php endforeach; ?>
                                                    </tr>

                                                    <!-- Skills & Competencies Section -->
                                                    <tr>
                                                        <td><strong>Skills & Competencies</strong></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td class="text-muted">See position requirements</td>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                    <tr>
                                                        <td><?= nl2br(esc($position['skills_competencies'] ?? 'Not specified')) ?></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td><?= nl2br(esc($applicant['profile']['skills_competencies'] ?? 'Not provided')) ?></td>
                                                        <?php endforeach; ?>
                                                    </tr>

                                                    <!-- Knowledge Section -->
                                                    <tr>
                                                        <td><strong>Knowledge</strong></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td class="text-muted">See position requirements</td>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                    <tr>
                                                        <td><?= nl2br(esc($position['knowledge'] ?? 'Not specified')) ?></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td><?= nl2br(esc($applicant['profile']['knowledge'] ?? 'Not provided')) ?></td>
                                                        <?php endforeach; ?>
                                                    </tr>

                                                    <!-- Remarks Section -->
                                                    <tr>
                                                        <td><strong>Remarks</strong></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td class="text-muted">Remarks/Comments</td>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                    <tr>
                                                        <td><?= nl2br(esc($position['remarks'] ?? 'Not specified')) ?></td>
                                                        <?php foreach ($pageApplicants as $applicant): ?>
                                                            <td><?= nl2br(esc($applicant['profile']['remarks'] ?? 'Not provided')) ?></td>
                                                        <?php endforeach; ?>
                                                    </tr>


                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Rating Scores Table -->
                                <?php if (!empty($rate_items)): ?>
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h6 class="mb-3">Rating Criteria and Scores</h6>
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-sm">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th width="25%">Criteria</th>
                                                            <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                                <th width="<?= 60 / count($pageApplicants) ?>%">
                                                                    Applicant <?= $startIndex + $i + 1 ?>
                                                                </th>
                                                            <?php endfor; ?>
                                                            <th width="15%">Out of</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php
                                                        $totalAchieved = array_fill(0, count($pageApplicants), 0);
                                                        $totalMax = 0;
                                                        ?>
                                                        <?php foreach ($rate_items as $rateItem): ?>
                                                            <tr>
                                                                <td><?= esc($rateItem['item_label']) ?></td>
                                                                <?php
                                                                $maxScore = 0;
                                                                foreach ($pageApplicants as $index => $applicant):
                                                                    $score = 0;
                                                                    $maxScoreForItem = 0;

                                                                    // Find rating for this applicant and rate item
                                                                    if (!empty($applicant['ratings'])) {
                                                                        foreach ($applicant['ratings'] as $rating) {
                                                                            if ($rating['rate_item_id'] == $rateItem['id']) {
                                                                                $score = $rating['score_achieved'];
                                                                                $maxScoreForItem = $rating['score_max'];
                                                                                break;
                                                                            }
                                                                        }
                                                                    }

                                                                    $totalAchieved[$index] += $score;
                                                                    if ($maxScoreForItem > $maxScore) {
                                                                        $maxScore = $maxScoreForItem;
                                                                    }
                                                                ?>
                                                                    <td class="text-center"><?= $score ?></td>
                                                                <?php endforeach; ?>
                                                                <td class="text-center"><strong><?= $maxScore ?></strong></td>
                                                                <?php $totalMax += $maxScore; ?>
                                                            </tr>
                                                        <?php endforeach; ?>

                                                        <!-- Total Row -->
                                                        <tr class="table-dark">
                                                            <td><strong>Total</strong></td>
                                                            <?php foreach ($totalAchieved as $total): ?>
                                                                <td class="text-center"><strong><?= $total ?></strong></td>
                                                            <?php endforeach; ?>
                                                            <td class="text-center"><strong><?= $totalMax ?></strong></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endfor; ?>

                        <!-- Pagination Controls -->
                        <?php if ($totalPages > 1): ?>
                            <div class="row mt-4 no-print">
                                <div class="col-12">
                                    <nav aria-label="Applicant navigation">
                                        <ul class="pagination justify-content-center">
                                            <li class="page-item" id="prev-btn">
                                                <a class="page-link" href="javascript:void(0)">Previous</a>
                                            </li>
                                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                                <li class="page-item <?= ($i == 1) ? 'active' : '' ?>" data-page="<?= $i ?>">
                                                    <a class="page-link" href="javascript:void(0)"><?= $i ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            <li class="page-item" id="next-btn">
                                                <a class="page-link" href="javascript:void(0)">Next</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Hidden form for PDF export -->
<form id="pdfExportForm" method="post" action="<?= base_url('reports/form37/positions/export') ?>" style="display: none;">
    <?= csrf_field() ?>
    <input type="hidden" name="position_id" value="<?= $position['id'] ?>">
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const totalPages = <?= $totalPages ?? 1 ?>;
    let currentPage = 1;

    // Function to show selected page and hide others
    function showPage(pageNum) {
        // Hide all pages
        document.querySelectorAll('.page-section').forEach(page => {
            page.style.display = 'none';
        });

        // Show selected page
        const selectedPage = document.getElementById('page-' + pageNum);
        if (selectedPage) {
            selectedPage.style.display = 'block';
        }

        // Update active class on pagination buttons
        document.querySelectorAll('.pagination .page-item').forEach(item => {
            if (item.dataset.page == pageNum) {
                item.classList.add('active');
            } else if (item.dataset.page) {
                item.classList.remove('active');
            }
        });

        // Update current page
        currentPage = pageNum;

        // Update previous/next button states
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        if (prevBtn) prevBtn.classList.toggle('disabled', currentPage <= 1);
        if (nextBtn) nextBtn.classList.toggle('disabled', currentPage >= totalPages);
    }

    // Add click handlers to page buttons
    document.querySelectorAll('.pagination .page-item[data-page]').forEach(item => {
        item.addEventListener('click', function() {
            showPage(parseInt(this.dataset.page));
        });
    });

    // Add click handlers to previous/next buttons
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');

    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            if (currentPage > 1) {
                showPage(currentPage - 1);
            }
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (currentPage < totalPages) {
                showPage(currentPage + 1);
            }
        });
    }

    // Initialize first page
    if (totalPages > 0) {
        showPage(1);
    }
});

// PDF Export functionality
function exportForm37PDF() {
    // Show loading state
    const exportBtn = document.querySelector('button[onclick="exportForm37PDF()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating PDF...';
    exportBtn.disabled = true;

    try {
        // Submit the form to trigger PDF download
        document.getElementById('pdfExportForm').submit();

        // Reset button after a short delay
        setTimeout(() => {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }, 2000);

    } catch (error) {
        console.error('PDF Export Error:', error);
        alert('Failed to generate PDF. Please try again.');

        // Reset button
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    }
}

// Print functionality
function printReport() {
    const previouslyVisiblePage = document.querySelector('.page-section[style*="block"]') ?
        parseInt(document.querySelector('.page-section[style*="block"]').id.split('-')[1]) : 1;

    // Add print-specific styles
    const style = document.createElement('style');
    style.id = 'print-style';
    style.innerHTML = `
        @media print {
            .page-section {
                display: block !important;
                page-break-after: always;
            }
            .pagination, .no-print, .breadcrumb, .btn {
                display: none !important;
            }
            body {
                font-size: 12px;
                line-height: 1.3;
            }
            table {
                font-size: 11px;
                border-collapse: collapse;
            }
            .table th, .table td {
                padding: 8px;
                border: 1px solid #000;
            }
            .card {
                box-shadow: none;
                border: none;
            }
            .badge {
                border: 1px solid #000;
                color: #000 !important;
                background-color: transparent !important;
            }
        }
    `;
    document.head.appendChild(style);

    // Show all pages for printing
    document.querySelectorAll('.page-section').forEach(page => {
        page.style.display = 'block';
    });

    // Print
    window.print();

    // Restore previous state after print
    setTimeout(function() {
        document.head.removeChild(style);
        showPage(previouslyVisiblePage);
    }, 1000);
}
</script>
<?= $this->endSection() ?>
