<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * RatingDataModel
 *
 * Model for the rating_data table
 * Handles rating data for applications within exercises and organizations
 */
class RatingDataModel extends Model
{
    protected $table         = 'rating_data';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false; // Using is_active flag instead
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'org_id',           // Organization reference
        'exercise_id',      // Exercise reference  
        'application_id',   // Application reference
        'rating_item_id',   // Rating item reference
        'rating_score',     // Score value (supports decimals)
        'rating_remarks',   // Remarks or comments
        'created_by',       // User who created the record
        'updated_by',       // Last user who updated the record
        'is_active'         // Active flag (1 = active, 0 = inactive)
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation rules
    protected $validationRules = [
        'org_id' => 'required|numeric',
        'exercise_id' => 'required|numeric',
        'application_id' => 'required|numeric',
        'rating_item_id' => 'required|numeric',
        'rating_score' => 'required|decimal|greater_than_equal_to[0]',
        'rating_remarks' => 'permit_empty',
        'created_by' => 'required|numeric',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];

    // Validation messages
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric'  => 'Exercise ID must be a number'
        ],
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'rating_item_id' => [
            'required' => 'Rating item ID is required',
            'numeric'  => 'Rating item ID must be a number'
        ],
        'rating_score' => [
            'required' => 'Rating score is required',
            'decimal'  => 'Rating score must be a valid decimal number',
            'greater_than_equal_to' => 'Rating score must be greater than or equal to 0'
        ],
        'created_by' => [
            'required' => 'Created by user ID is required',
            'numeric'  => 'Created by must be a number'
        ],
        'is_active' => [
            'in_list' => 'Active status must be 0 or 1'
        ]
    ];

    // Skip validation for these operations
    protected $skipValidation = false;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedBy'];
    protected $beforeUpdate   = ['setUpdatedBy'];

    /**
     * Set created_by field before insert
     *
     * @param array $data
     * @return array
     */
    protected function setCreatedBy(array $data)
    {
        if (!isset($data['data']['created_by'])) {
            $data['data']['created_by'] = session()->get('user_id') ?? 1;
        }
        return $data;
    }

    /**
     * Set updated_by field before update
     *
     * @param array $data
     * @return array
     */
    protected function setUpdatedBy(array $data)
    {
        $data['data']['updated_by'] = session()->get('user_id') ?? 1;
        return $data;
    }

    /**
     * Get all active rating data
     *
     * @return array
     */
    public function getActiveRatings()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get rating data by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getRatingsByOrganization($orgId)
    {
        return $this->where('org_id', $orgId)
                    ->where('is_active', 1)
                    ->findAll();
    }

    /**
     * Get rating data by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getRatingsByExercise($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->where('is_active', 1)
                    ->findAll();
    }

    /**
     * Get rating data by application ID
     *
     * @param int $applicationId
     * @return array
     */
    public function getRatingsByApplication($applicationId)
    {
        return $this->where('application_id', $applicationId)
                    ->where('is_active', 1)
                    ->findAll();
    }

    /**
     * Get rating data by rating item ID
     *
     * @param int $ratingItemId
     * @return array
     */
    public function getRatingsByItem($ratingItemId)
    {
        return $this->where('rating_item_id', $ratingItemId)
                    ->where('is_active', 1)
                    ->findAll();
    }

    /**
     * Get specific rating data by multiple criteria
     *
     * @param int $applicationId
     * @param int $ratingItemId
     * @param int $exerciseId
     * @return array|null
     */
    public function getRatingByApplicationAndItem($applicationId, $ratingItemId, $exerciseId = null)
    {
        $builder = $this->where('application_id', $applicationId)
                        ->where('rating_item_id', $ratingItemId)
                        ->where('is_active', 1);
        
        if ($exerciseId !== null) {
            $builder->where('exercise_id', $exerciseId);
        }
        
        return $builder->first();
    }

    /**
     * Get rating data with related information (joins)
     *
     * @param int $applicationId
     * @return array
     */
    public function getRatingsWithDetails($applicationId)
    {
        return $this->select('rating_data.*, rate_items.item_label, rate_items_scores.label as score_label, rate_items_scores.score_description')
                    ->join('rate_items', 'rate_items.id = rating_data.rating_item_id', 'left')
                    ->join('rate_items_scores', 'rate_items_scores.score = rating_data.rating_score AND rate_items_scores.item_id = rating_data.rating_item_id', 'left')
                    ->where('rating_data.application_id', $applicationId)
                    ->where('rating_data.is_active', 1)
                    ->findAll();
    }

    /**
     * Calculate average rating score for an application
     *
     * @param int $applicationId
     * @return float
     */
    public function getAverageRatingByApplication($applicationId)
    {
        $result = $this->selectAvg('rating_score', 'avg_score')
                       ->where('application_id', $applicationId)
                       ->where('is_active', 1)
                       ->first();
        
        return $result['avg_score'] ?? 0.0;
    }

    /**
     * Calculate total rating score for an application
     *
     * @param int $applicationId
     * @return float
     */
    public function getTotalRatingByApplication($applicationId)
    {
        $result = $this->selectSum('rating_score', 'total_score')
                       ->where('application_id', $applicationId)
                       ->where('is_active', 1)
                       ->first();
        
        return $result['total_score'] ?? 0.0;
    }

    /**
     * Soft delete rating data (set is_active to 0)
     *
     * @param int $id
     * @param int $deletedBy
     * @return bool
     */
    public function softDelete($id, $deletedBy = null)
    {
        $data = [
            'is_active' => 0,
            'updated_by' => $deletedBy ?? session()->get('user_id') ?? 1
        ];
        
        return $this->update($id, $data);
    }

    /**
     * Restore soft deleted rating data (set is_active to 1)
     *
     * @param int $id
     * @param int $restoredBy
     * @return bool
     */
    public function restore($id, $restoredBy = null)
    {
        $data = [
            'is_active' => 1,
            'updated_by' => $restoredBy ?? session()->get('user_id') ?? 1
        ];
        
        return $this->update($id, $data);
    }

    /**
     * Get rating statistics for an exercise
     *
     * @param int $exerciseId
     * @return array
     */
    public function getRatingStatsByExercise($exerciseId)
    {
        return $this->select('COUNT(*) as total_ratings, AVG(rating_score) as avg_score, MIN(rating_score) as min_score, MAX(rating_score) as max_score')
                    ->where('exercise_id', $exerciseId)
                    ->where('is_active', 1)
                    ->first();
    }

    /**
     * Check if rating exists for application and item
     *
     * @param int $applicationId
     * @param int $ratingItemId
     * @param int $exerciseId
     * @return bool
     */
    public function ratingExists($applicationId, $ratingItemId, $exerciseId)
    {
        $count = $this->where('application_id', $applicationId)
                      ->where('rating_item_id', $ratingItemId)
                      ->where('exercise_id', $exerciseId)
                      ->where('is_active', 1)
                      ->countAllResults();
        
        return $count > 0;
    }

    /**
     * Bulk insert rating data
     *
     * @param array $data
     * @return bool
     */
    public function bulkInsert($data)
    {
        return $this->insertBatch($data);
    }

    /**
     * Get rating data by user (created_by)
     *
     * @param int $userId
     * @return array
     */
    public function getRatingsByUser($userId)
    {
        return $this->where('created_by', $userId)
                    ->where('is_active', 1)
                    ->findAll();
    }
}
