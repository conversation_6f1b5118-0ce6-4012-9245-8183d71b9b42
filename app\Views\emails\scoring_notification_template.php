<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Application Scoring Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(to bottom left, #F00F00 0%, #D00D00 40%, #000000 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom left, rgba(240, 15, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
        }
        .header h1 {
            position: relative;
            z-index: 1;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .content {
            padding: 30px 25px;
            background: white;
        }
        .highlight-text {
            color: #F00F00;
            font-weight: bold;
        }
        .notification-box {
            border-left: 4px solid #F00F00;
            padding-left: 15px;
            margin: 20px 0;
            background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
        }
        .notification-box h2 {
            color: #F00F00;
            margin-top: 0;
            font-size: 20px;
            font-weight: bold;
        }
        .position-info {
            border-left: 4px solid #F00F00;
            padding-left: 15px;
            margin: 20px 0;
            background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
        }
        .position-info h3 {
            color: #F00F00;
            margin-top: 0;
            font-size: 16px;
            font-weight: bold;
        }
        .position-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px 15px;
            margin: 10px 0;
        }
        .position-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }
        .position-reference {
            font-size: 14px;
            color: #666;
            font-style: italic;
        }
        .exercise-highlight {
            color: #F00F00;
            font-weight: bold;
            font-size: 18px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            font-size: 12px;
            color: #666;
            background: linear-gradient(to bottom left, rgba(240, 15, 0, 0.03) 0%, rgba(0, 0, 0, 0.08) 100%);
            border-radius: 0 0 10px 10px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>Scoring Notification</h1>
        </div>

        <div class="content">
            <p>Dear <span class="highlight-text"><?= esc($applicant_name) ?></span>,</p>

            <p>We are writing to inform you that your application for the position with <span class="highlight-text"><?= esc($organization['org_name']) ?></span> under <span class="exercise-highlight"><?= esc($exercise['exercise_name']) ?></span> is now going through the scoring stage of the recruitment process.</p>

            <div class="notification-box">
                <h2>Scoring Process</h2>
                <p>During the scoring stage, our recruitment team will evaluate your application against specific criteria and assign scores based on your qualifications, experience, and suitability for the position. This systematic assessment ensures a fair and objective evaluation of all candidates.</p>
            </div>

            <div class="position-info">
                <h3>Applied Position</h3>
                <div class="position-item">
                    <div class="position-title"><?= esc($position['designation']) ?></div>
                    <?php if (!empty($position['position_reference'])): ?>
                        <div class="position-reference">Reference: <?= esc($position['position_reference']) ?></div>
                    <?php endif; ?>
                </div>
            </div>

            <p>This is an important milestone in our selection process, and we appreciate your patience as we carefully review all applications.</p>

            <p>Please ensure your contact details remain current so we can reach you with updates regarding the progress of your application.</p>

            <p>Best regards,<br><strong>The DERS Team</strong></p>
        </div>

        <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
            <p>&copy; <?= date('Y') ?> DERS. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
