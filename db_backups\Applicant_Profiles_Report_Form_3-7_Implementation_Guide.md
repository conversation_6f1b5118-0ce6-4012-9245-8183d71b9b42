# Applicant Profiles Report Implementation Guide

## Overview
This guide provides a comprehensive implementation of an Applicant Profiles Report system similar to the SelMasta system. The report displays a comparative analysis between position specifications and person specifications in a structured, paginated format suitable for recruitment and selection processes.

## System Architecture

### 1. Core Components
- **Controller**: Handles data retrieval and processing
- **Model**: Manages applicant and position data
- **View**: Displays comparative profile tables
- **Pagination**: Client-side pagination for better performance
- **Print Functionality**: Print-optimized layout

### 2. Key Features
- **Comparative Analysis**: Position vs Person specifications
- **Paginated Display**: 3 applicants per page
- **Rating System**: Numerical scoring with maximum values
- **Print Support**: Professional print layout
- **Responsive Design**: Mobile-friendly interface
- **Status Categorization**: Shortlisted, Eliminated, Withdrawn

## Database Structure

### Applicants Table
```sql
CREATE TABLE applicants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    org_id INT NOT NULL,
    position_id INT NOT NULL,
    position_group_id INT,
    
    -- Personal Information
    name VARCHAR(255) NOT NULL,
    sex ENUM('Male', 'Female') NOT NULL,
    age INT,
    place_origin VARCHAR(255),
    contact_details TEXT,
    nid_number VARCHAR(50),
    current_employer VARCHAR(255),
    current_position VARCHAR(255),
    address_location TEXT,
    
    -- Qualifications & Experience
    qualification_text TEXT,
    other_trainings TEXT,
    knowledge TEXT,
    skills_competencies TEXT,
    job_experiences TEXT,
    publications TEXT,
    awards TEXT,
    referees TEXT,
    comments TEXT,
    
    -- Rating Scores
    rate_age DECIMAL(5,2) DEFAULT 0,
    max_rate_age DECIMAL(5,2) DEFAULT 0,
    rate_qualification DECIMAL(5,2) DEFAULT 0,
    max_rate_qualification DECIMAL(5,2) DEFAULT 0,
    rate_experience DECIMAL(5,2) DEFAULT 0,
    max_rate_experience DECIMAL(5,2) DEFAULT 0,
    
    -- Experience Categories
    rate_private_non_relevant DECIMAL(5,2) DEFAULT 0,
    max_rate_private_non_relevant DECIMAL(5,2) DEFAULT 0,
    rate_private_relevant DECIMAL(5,2) DEFAULT 0,
    max_rate_private_relevant DECIMAL(5,2) DEFAULT 0,
    rate_public_non_relevant DECIMAL(5,2) DEFAULT 0,
    max_rate_public_non_relevant DECIMAL(5,2) DEFAULT 0,
    rate_public_relevant DECIMAL(5,2) DEFAULT 0,
    max_rate_public_relevant DECIMAL(5,2) DEFAULT 0,
    
    -- Other Rating Categories
    rate_trainings DECIMAL(5,2) DEFAULT 0,
    max_rate_trainings DECIMAL(5,2) DEFAULT 0,
    rate_skills_competencies DECIMAL(5,2) DEFAULT 0,
    max_rate_skills_competencies DECIMAL(5,2) DEFAULT 0,
    rate_knowledge DECIMAL(5,2) DEFAULT 0,
    max_rate_knowledge DECIMAL(5,2) DEFAULT 0,
    rate_public_service DECIMAL(5,2) DEFAULT 0,
    max_rate_public_service DECIMAL(5,2) DEFAULT 0,
    rate_capability DECIMAL(5,2) DEFAULT 0,
    max_rate_capability DECIMAL(5,2) DEFAULT 0,
    
    -- Totals
    rate_total DECIMAL(8,2) DEFAULT 0,
    max_rate_total DECIMAL(8,2) DEFAULT 0,
    
    -- Status Information
    application_status ENUM('Active', 'Shortlisted', 'Eliminated', 'Withdrawn') DEFAULT 'Active',
    app_status_reason TEXT,
    pre_select_status VARCHAR(100),
    interview_notices TEXT,
    remarks TEXT,
    
    -- Audit Fields
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Positions Table
```sql
CREATE TABLE positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    org_id INT NOT NULL,
    position_group_id INT,
    position_no VARCHAR(100),
    designation VARCHAR(255) NOT NULL,
    classification VARCHAR(100),
    qualifications TEXT,
    job_experiences TEXT,
    skills_competencies TEXT,
    knowledge TEXT,
    other_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Organizations Table
```sql
CREATE TABLE organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    org_name VARCHAR(255) NOT NULL,
    org_logo VARCHAR(255),
    advertisement_no VARCHAR(100),
    advertisement_date DATE,
    mode_of_advert VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Backend Implementation (CodeIgniter 4)

### Controller: Reports.php
```php
<?php
namespace App\Controllers;

use App\Models\ApplicantModel;
use App\Models\PositionModel;
use App\Models\OrganizationModel;

class Reports extends BaseController
{
    protected $applicantModel;
    protected $positionModel;
    protected $organizationModel;

    public function __construct()
    {
        $this->applicantModel = new ApplicantModel();
        $this->positionModel = new PositionModel();
        $this->organizationModel = new OrganizationModel();
    }

    public function applicantProfiles($positionId)
    {
        // Validate position exists and belongs to current organization
        $position = $this->positionModel->where('org_id', session('org_id'))
                                       ->find($positionId);
        
        if (!$position) {
            return redirect()->to(base_url('reports'))
                           ->with('error', 'Position not found.');
        }

        // Get all applicants for this position
        $applicants = $this->applicantModel->where('position_id', $positionId)
                                          ->where('org_id', session('org_id'))
                                          ->findAll();

        // Sort applicants by status and rating
        $shortlisted = [];
        $eliminated = [];
        $withdrawn = [];
        $others = [];

        foreach ($applicants as $applicant) {
            switch ($applicant['application_status']) {
                case 'Shortlisted':
                    $shortlisted[] = $applicant;
                    break;
                case 'Eliminated':
                    $eliminated[] = $applicant;
                    break;
                case 'Withdrawn':
                    $withdrawn[] = $applicant;
                    break;
                default:
                    $others[] = $applicant;
                    break;
            }
        }

        // Sort each group by total rating (descending)
        $sortByRating = function($a, $b) {
            return ($b['rate_total'] ?? 0) <=> ($a['rate_total'] ?? 0);
        };

        usort($shortlisted, $sortByRating);
        usort($eliminated, $sortByRating);
        usort($withdrawn, $sortByRating);
        usort($others, $sortByRating);

        // Combine all groups in priority order
        $sortedApplicants = array_merge($shortlisted, $eliminated, $withdrawn, $others);

        // Get organization data
        $organization = $this->organizationModel->find(session('org_id'));

        // Prepare data for view
        $data = [
            'title' => $position['position_no'] . ' RS3-7',
            'menu' => 'reports',
            'position' => $position,
            'applicants' => $sortedApplicants,
            'org' => $organization
        ];

        return view('reports/applicant_profiles', $data);
    }
}
```

### Model: ApplicantModel.php
```php
<?php
namespace App\Models;

use CodeIgniter\Model;

class ApplicantModel extends Model
{
    protected $table = 'applicants';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    
    protected $allowedFields = [
        'org_id', 'position_id', 'position_group_id',
        'name', 'sex', 'age', 'place_origin', 'contact_details', 'nid_number',
        'current_employer', 'current_position', 'address_location',
        'qualification_text', 'other_trainings', 'knowledge', 'skills_competencies',
        'job_experiences', 'publications', 'awards', 'referees', 'comments',
        'rate_age', 'max_rate_age', 'rate_qualification', 'max_rate_qualification',
        'rate_experience', 'max_rate_experience', 'rate_private_non_relevant',
        'max_rate_private_non_relevant', 'rate_private_relevant', 'max_rate_private_relevant',
        'rate_public_non_relevant', 'max_rate_public_non_relevant',
        'rate_public_relevant', 'max_rate_public_relevant',
        'rate_trainings', 'max_rate_trainings', 'rate_skills_competencies',
        'max_rate_skills_competencies', 'rate_knowledge', 'max_rate_knowledge',
        'rate_public_service', 'max_rate_public_service', 'rate_capability',
        'max_rate_capability', 'rate_total', 'max_rate_total',
        'application_status', 'app_status_reason', 'pre_select_status',
        'interview_notices', 'remarks', 'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $useSoftDeletes = false;
}
```

## Frontend Implementation

### Routes Configuration
```php
// In app/Config/Routes.php
$routes->group('reports', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->get('/', 'Reports::index');
    $routes->get('viewPositions/(:num)', 'Reports::viewPositions/$1');
    $routes->get('applicantProfiles/(:num)', 'Reports::applicantProfiles/$1');
});
```

### View: applicant_profiles.php
```php
<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item active">Applicant Profiles</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="invoice p-3 mb-3">
                        <!-- Header Section -->
                        <div class="row">
                            <div class="col-12 text-center">
                                <img src="<?= base_url('public/assets/system_img/organization_logo.jpg') ?>"
                                     alt="Organization Logo" style="width: 100px;">
                                <h4 class="mt-3"><?= strtoupper(esc($org['org_name'])) ?></h4>
                                <h5>APPLICANT PROFILE REPORT</h5>
                                <p class="float-right">FORM RS 3.7</p>
                            </div>
                        </div>

                        <!-- Information Section -->
                        <div class="row invoice-info mt-4">
                            <div class="col-sm-6 invoice-col">
                                <b>Advertisement No:</b> <span><?= esc($org['advertisement_no']) ?></span><br>
                                <b>Advertisement Date:</b> <span><?= esc($org['advertisement_date']) ?></span><br>
                                <b>Mode of Advert:</b> <span><?= esc($org['mode_of_advert']) ?></span><br>
                                <b>Total No. Of Applicants:</b> <span><?= count($applicants) ?></span>
                            </div>
                            <div class="col-sm-6 invoice-col">
                                <b>Position No:</b> <span><?= esc($position['position_no']) ?></span><br>
                                <b>Designation:</b> <span><?= esc($position['designation']) ?></span><br>
                                <b>Classification:</b> <span><?= esc($position['classification']) ?></span>
                            </div>
                        </div>

                        <?php
                        // Pagination setup
                        $totalApplicants = count($applicants);
                        $applicantsPerPage = 3;
                        $totalPages = ceil($totalApplicants / $applicantsPerPage);
                        ?>

                        <!-- Paginated Content -->
                        <?php for ($page = 1; $page <= $totalPages; $page++): ?>
                            <?php
                            $startIndex = ($page - 1) * $applicantsPerPage;
                            $pageApplicants = array_slice($applicants, $startIndex, $applicantsPerPage);
                            ?>

                            <div class="page-section" id="page-<?= $page ?>" style="<?= $page > 1 ? 'display: none;' : '' ?>">
                                <!-- Position vs Person Specifications Table -->
                                <div class="row mt-4">
                                    <div class="col-12 table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>POSITION SPECIFICATION</th>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <th>PERSON SPECIFICATION<br>Applicant <?= $startIndex + $i + 1 ?></th>
                                                    <?php endfor; ?>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Personal Information Row -->
                                                <tr>
                                                    <td></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td>
                                                            <b>Name:</b> <?= esc($pageApplicants[$i]['name']) ?><br>
                                                            <b>Sex:</b> <?= esc($pageApplicants[$i]['sex']) ?><br>
                                                            <b>Age:</b> <?= esc($pageApplicants[$i]['age']) ?><br>
                                                            <b>Current Position:</b> <?= esc($pageApplicants[$i]['current_position']) ?><br>
                                                            <b>Address/Location:</b> <?= esc($pageApplicants[$i]['address_location']) ?><br>
                                                        </td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <!-- Qualifications Section -->
                                                <tr>
                                                    <td>Qualifications (from Job Description):</td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td><strong><?= esc($position['qualifications']) ?></strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['qualification_text'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <!-- Training Section -->
                                                <tr>
                                                    <td><strong>Other training/courses attended</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['other_trainings'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <!-- Knowledge Section -->
                                                <tr>
                                                    <td><strong>Knowledge</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td><?= esc($position['knowledge']) ?></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['knowledge'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <!-- Skills & Competencies Section -->
                                                <tr>
                                                    <td><strong>Skills & Competencies</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td><?= esc($position['skills_competencies']) ?></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['skills_competencies'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <!-- Job Experience Section -->
                                                <tr>
                                                    <td><strong>Related Job Experience</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td><?= esc($position['job_experiences']) ?></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['job_experiences'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <!-- Comments Section -->
                                                <tr>
                                                    <td><strong>Comments</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['comments'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Rating Scores Table -->
                                <div class="row mt-4">
                                    <div class="col-12 table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Criteria</th>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <th>Applicant <?= $startIndex + $i + 1 ?></th>
                                                    <?php endfor; ?>
                                                    <th>Out of</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Age</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_age']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_age'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Qualification</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_qualification']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_qualification'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Experience</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?=
                                                            ($applicant['rate_private_non_relevant'] ?? 0) +
                                                            ($applicant['rate_private_relevant'] ?? 0) +
                                                            ($applicant['rate_public_non_relevant'] ?? 0) +
                                                            ($applicant['rate_public_relevant'] ?? 0)
                                                        ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?=
                                                        ($applicant['max_rate_private_non_relevant'] ?? 0) +
                                                        ($applicant['max_rate_private_relevant'] ?? 0) +
                                                        ($applicant['max_rate_public_non_relevant'] ?? 0) +
                                                        ($applicant['max_rate_public_relevant'] ?? 0)
                                                    ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Trainings</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_trainings']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_trainings'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Skills & Competencies</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_skills_competencies']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_skills_competencies'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Knowledge</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_knowledge']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_knowledge'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Public Service Status</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_public_service']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_public_service'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Capability</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_capability']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_capability'] ?? 0 ?></td>
                                                </tr>
                                                <tr class="font-weight-bold bg-light">
                                                    <td>Total</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_total']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_total'] ?? 0 ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>

                        <!-- Pagination Controls -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <nav aria-label="Applicant navigation">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item" id="prev-btn">
                                            <a class="page-link" href="javascript:void(0)">Previous</a>
                                        </li>
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?= ($i == 1) ? 'active' : '' ?>" data-page="<?= $i ?>">
                                                <a class="page-link" href="javascript:void(0)"><?= $i ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        <li class="page-item" id="next-btn">
                                            <a class="page-link" href="javascript:void(0)">Next</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>

                        <!-- Print Button -->
                        <div class="row no-print mt-4">
                            <div class="col-12">
                                <a href="javascript:void(0);" id="print-btn" class="btn btn-default">
                                    <i class="fas fa-print"></i> Print
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- JavaScript for Pagination and Print -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const totalPages = <?= $totalPages ?>;
    let currentPage = 1;

    // Function to show selected page and hide others
    function showPage(pageNum) {
        // Hide all pages
        document.querySelectorAll('.page-section').forEach(page => {
            page.style.display = 'none';
        });

        // Show selected page
        const selectedPage = document.getElementById('page-' + pageNum);
        if (selectedPage) {
            selectedPage.style.display = 'block';
        }

        // Update active class on pagination buttons
        document.querySelectorAll('.pagination .page-item').forEach(item => {
            if (item.dataset.page == pageNum) {
                item.classList.add('active');
            } else if (item.dataset.page) {
                item.classList.remove('active');
            }
        });

        // Update current page
        currentPage = pageNum;

        // Update previous/next button states
        document.getElementById('prev-btn').classList.toggle('disabled', currentPage <= 1);
        document.getElementById('next-btn').classList.toggle('disabled', currentPage >= totalPages);
    }

    // Add click handlers to page buttons
    document.querySelectorAll('.pagination .page-item[data-page]').forEach(item => {
        item.addEventListener('click', function() {
            showPage(parseInt(this.dataset.page));
        });
    });

    // Add click handlers to previous/next buttons
    document.getElementById('prev-btn').addEventListener('click', function() {
        if (currentPage > 1) {
            showPage(currentPage - 1);
        }
    });

    document.getElementById('next-btn').addEventListener('click', function() {
        if (currentPage < totalPages) {
            showPage(currentPage + 1);
        }
    });

    // Print functionality
    document.getElementById('print-btn').addEventListener('click', function() {
        const previouslyVisiblePage = currentPage;

        // Add print-specific styles
        const style = document.createElement('style');
        style.id = 'print-style';
        style.innerHTML = `
            @media print {
                .page-section {
                    display: block !important;
                    page-break-after: always;
                }
                .pagination, .no-print {
                    display: none !important;
                }
                body { font-size: 12px; }
                table { font-size: 11px; }
            }
        `;
        document.head.appendChild(style);

        // Show all pages for printing
        document.querySelectorAll('.page-section').forEach(page => {
            page.style.display = 'block';
        });

        // Print
        window.print();

        // Restore previous state after print
        setTimeout(function() {
            document.head.removeChild(style);
            showPage(previouslyVisiblePage);
        }, 1000);
    });

    // Initialize first page
    showPage(1);
});
</script>
<?= $this->endSection() ?>
```

## CSS Styling

### Custom Styles for Applicant Profiles
```css
/* Add to your main CSS file or in a <style> section */

/* Print-specific styles */
@media print {
    .page-section {
        display: block !important;
        page-break-after: always;
    }

    .pagination, .no-print, .breadcrumb {
        display: none !important;
    }

    body {
        font-size: 12px;
        line-height: 1.3;
    }

    table {
        font-size: 11px;
        border-collapse: collapse;
    }

    .table th, .table td {
        padding: 8px;
        border: 1px solid #000;
    }

    .invoice {
        box-shadow: none;
        border: none;
    }
}

/* Screen styles */
@media screen {
    .page-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        background-color: #fff;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: bold;
        text-align: center;
        vertical-align: middle;
    }

    .table td {
        vertical-align: top;
        word-wrap: break-word;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        cursor: auto;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 12px;
    }

    .invoice-info {
        text-align: center;
    }

    .invoice-info .col-sm-6 {
        margin-bottom: 15px;
    }
}
```

## Advanced Features

### 1. Export to PDF (Optional)
```php
// Add to your controller
public function exportToPDF($positionId)
{
    // Use libraries like TCPDF or mPDF
    $data = $this->getApplicantProfilesData($positionId);

    // Generate PDF
    $pdf = new \TCPDF();
    $pdf->AddPage();
    $html = view('reports/applicant_profiles_pdf', $data);
    $pdf->writeHTML($html, true, false, true, false, '');
    $pdf->Output('applicant_profiles.pdf', 'D');
}
```

### 2. Search and Filter Functionality
```javascript
// Add search functionality
function addSearchFilter() {
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search applicants...';
    searchInput.className = 'form-control mb-3';

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterApplicants(searchTerm);
    });

    // Insert before pagination
    const paginationRow = document.querySelector('.pagination').closest('.row');
    paginationRow.parentNode.insertBefore(searchInput, paginationRow);
}

function filterApplicants(searchTerm) {
    // Implementation for filtering applicants
    // This would require modifying the pagination logic
}
```

### 3. Status-based Color Coding
```css
/* Add status-based styling */
.applicant-shortlisted {
    background-color: #d4edda !important;
    border-left: 4px solid #28a745;
}

.applicant-eliminated {
    background-color: #f8d7da !important;
    border-left: 4px solid #dc3545;
}

.applicant-withdrawn {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107;
}
```

### 4. Dynamic Column Visibility
```javascript
// Add column toggle functionality
function addColumnToggle() {
    const toggleContainer = document.createElement('div');
    toggleContainer.className = 'mb-3';
    toggleContainer.innerHTML = `
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleColumn('qualifications')">
                Qualifications
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleColumn('experience')">
                Experience
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleColumn('ratings')">
                Ratings
            </button>
        </div>
    `;

    // Insert before first page section
    const firstPageSection = document.querySelector('.page-section');
    firstPageSection.parentNode.insertBefore(toggleContainer, firstPageSection);
}

function toggleColumn(columnType) {
    // Implementation for showing/hiding specific sections
    const sections = document.querySelectorAll(`[data-section="${columnType}"]`);
    sections.forEach(section => {
        section.style.display = section.style.display === 'none' ? '' : 'none';
    });
}
```

## Implementation Best Practices

### 1. Performance Optimization
- **Lazy Loading**: Load only visible pages initially
- **Caching**: Cache applicant data to reduce database queries
- **Pagination**: Use client-side pagination for better UX
- **Image Optimization**: Compress logos and images

### 2. Security Considerations
```php
// Input validation and sanitization
public function applicantProfiles($positionId)
{
    // Validate position ID
    if (!is_numeric($positionId) || $positionId <= 0) {
        throw new \InvalidArgumentException('Invalid position ID');
    }

    // Check user permissions
    if (!$this->hasPermission('view_reports')) {
        return redirect()->to(base_url('dashboard'))
                       ->with('error', 'Access denied');
    }

    // Sanitize output
    $data['applicants'] = array_map(function($applicant) {
        return array_map('esc', $applicant);
    }, $data['applicants']);
}
```

### 3. Accessibility Features
```html
<!-- Add ARIA labels and roles -->
<table class="table table-striped table-bordered" role="table" aria-label="Applicant comparison table">
    <thead>
        <tr role="row">
            <th scope="col" role="columnheader">Position Specification</th>
            <th scope="col" role="columnheader">Person Specification</th>
        </tr>
    </thead>
    <tbody>
        <tr role="row">
            <td role="cell">Content</td>
            <td role="cell">Content</td>
        </tr>
    </tbody>
</table>
```

### 4. Mobile Responsiveness
```css
/* Mobile-first responsive design */
@media (max-width: 576px) {
    .table-responsive {
        border: none;
    }

    .table-responsive table,
    .table-responsive thead,
    .table-responsive tbody,
    .table-responsive th,
    .table-responsive td,
    .table-responsive tr {
        display: block;
    }

    .table-responsive thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .table-responsive tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
    }

    .table-responsive td {
        border: none;
        position: relative;
        padding-left: 50%;
    }

    .table-responsive td:before {
        content: attr(data-label) ": ";
        position: absolute;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: bold;
    }
}
```

## Testing Guidelines

### 1. Unit Tests
```php
class ApplicantProfilesTest extends CIUnitTestCase
{
    public function testApplicantProfilesWithValidPosition()
    {
        $controller = new Reports();
        $result = $controller->applicantProfiles(1);

        $this->assertInstanceOf(ResponseInterface::class, $result);
    }

    public function testApplicantProfilesWithInvalidPosition()
    {
        $controller = new Reports();
        $result = $controller->applicantProfiles(999);

        // Should redirect with error
        $this->assertEquals(302, $result->getStatusCode());
    }
}
```

### 2. Integration Tests
```javascript
// Frontend testing with Jest or similar
describe('Applicant Profiles Pagination', () => {
    test('should show first page by default', () => {
        const firstPage = document.getElementById('page-1');
        expect(firstPage.style.display).not.toBe('none');
    });

    test('should navigate to next page', () => {
        const nextBtn = document.getElementById('next-btn');
        nextBtn.click();

        const secondPage = document.getElementById('page-2');
        expect(secondPage.style.display).not.toBe('none');
    });
});
```

## Deployment Considerations

### 1. Environment Configuration
```php
// In .env file
REPORTS_APPLICANTS_PER_PAGE=3
REPORTS_ENABLE_PDF_EXPORT=true
REPORTS_CACHE_DURATION=3600
```

### 2. Database Indexing
```sql
-- Add indexes for better performance
CREATE INDEX idx_applicants_position_org ON applicants(position_id, org_id);
CREATE INDEX idx_applicants_status ON applicants(application_status);
CREATE INDEX idx_positions_org ON positions(org_id);
```

### 3. Caching Strategy
```php
// Implement caching for better performance
public function applicantProfiles($positionId)
{
    $cacheKey = "applicant_profiles_{$positionId}_" . session('org_id');

    if (cache($cacheKey)) {
        $data = cache($cacheKey);
    } else {
        $data = $this->generateApplicantProfilesData($positionId);
        cache($cacheKey, $data, 3600); // Cache for 1 hour
    }

    return view('reports/applicant_profiles', $data);
}
```

## Conclusion

This implementation provides a comprehensive applicant profiles report system with:

- **Professional Layout**: Clean, printable format suitable for official reports
- **Comparative Analysis**: Side-by-side comparison of position vs person specifications
- **Pagination**: Efficient handling of large applicant lists
- **Rating System**: Numerical scoring with maximum values display
- **Print Support**: Optimized for professional printing
- **Responsive Design**: Works on all device sizes
- **Performance Optimized**: Client-side pagination and caching
- **Extensible**: Easy to add new features and customizations

The system can be easily adapted for different recruitment processes while maintaining the core comparative analysis functionality.
