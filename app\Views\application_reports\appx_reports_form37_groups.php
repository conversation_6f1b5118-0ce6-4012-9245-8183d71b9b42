<?php
/**
 * View file for Form 3.7 Profiling Report - Position Groups List
 *
 * @var array $exercise Exercise details
 * @var array $position_groups List of position groups with position counts
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Form 3.7 Profiling - <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Form 3.7 Profiling Report</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Groups List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        Select Position Group
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($position_groups)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Position Groups Found</h5>
                            <p class="text-muted">There are no position groups available for this exercise.</p>
                            <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="positionGroupsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="40%">Position Group Name</th>
                                        <th width="20%">Organization</th>
                                        <th width="15%">Number of Positions</th>
                                        <th width="20%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($position_groups as $group): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($group['group_name']) ?></strong>
                                                    <?php if (!empty($group['description'])): ?>
                                                        <br>
                                                        <small class="text-muted"><?= esc($group['description']) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= esc($group['org_name'] ?? 'N/A') ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info fs-6">
                                                    <?= $group['position_count'] ?> Position<?= $group['position_count'] != 1 ? 's' : '' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('reports/form37/groups/' . $group['id']) ?>" 
                                                   class="btn btn-success btn-sm">
                                                    <i class="fas fa-arrow-right me-1"></i>
                                                    View Positions
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Exercise Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Exercise Name:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['exercise_name']) ?></dd>
                                <dt class="col-sm-4">Gazette No:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['gazzetted_no']) ?></dd>
                                <dt class="col-sm-4">Advertisement No:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['advertisement_no']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-success"><?= ucfirst($exercise['status']) ?></span>
                                </dd>
                                <dt class="col-sm-4">Created:</dt>
                                <dd class="col-sm-8"><?= date('M d, Y', strtotime($exercise['created_at'])) ?></dd>
                                <dt class="col-sm-4">Description:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['description']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionGroupsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']], // Sort by group name
        columnDefs: [
            { orderable: false, targets: [4] } // Disable sorting for Actions column
        ]
    });
});
</script>
<?= $this->endSection() ?>
