<?php
/**
 * View file for Scoring Report - Detailed Scoring View
 *
 * @var array $application Application details
 * @var array $rating_summary Rating summary with detailed scores
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $application['exercise_id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/scoring/' . $application['exercise_id']) ?>">Scoring Report</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/scoring/groups/' . $application['position_group_id']) ?>"><?= esc($application['group_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/scoring/positions/' . $application['position_id']) ?>"><?= esc($application['designation']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($application['first_name'] . ' ' . $application['last_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Scoring Details</h2>
                    <p class="text-muted mb-0"><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/scoring/positions/' . $application['position_id']) ?>" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Applications
                    </a>
                    <button type="button" class="btn btn-danger" onclick="exportScoringDetailPDF()">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Applicant Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        Applicant Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Full Name:</dt>
                                <dd class="col-sm-8"><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></dd>
                                <dt class="col-sm-4">Application No:</dt>
                                <dd class="col-sm-8"><strong><?= esc($application['application_number']) ?></strong></dd>
                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8"><?= esc($application['email_address']) ?></dd>
                                <dt class="col-sm-4">Gender:</dt>
                                <dd class="col-sm-8"><?= esc($application['gender']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($application['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($application['position_reference']) ?></dd>
                                <dt class="col-sm-4">Organization:</dt>
                                <dd class="col-sm-8"><?= esc($application['org_name']) ?></dd>
                                <dt class="col-sm-4">Exercise:</dt>
                                <dd class="col-sm-8"><?= esc($application['exercise_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scoring Summary -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <h3 class="text-primary mb-1"><?= number_format($rating_summary['total_achieved'], 1) ?></h3>
                    <p class="text-muted mb-0">Total Score Achieved</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <h3 class="text-secondary mb-1"><?= number_format($rating_summary['total_max'], 1) ?></h3>
                    <p class="text-muted mb-0">Maximum Possible Score</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <h3 class="text-primary mb-1"><?= number_format($rating_summary['percentage'], 1) ?>%</h3>
                    <p class="text-muted mb-0">Overall Percentage</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Scoring -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star me-2"></i>
                        Detailed Scoring Breakdown
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($rating_summary['ratings'])): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Scoring Data Available</h5>
                            <p class="text-muted">This application has not been scored yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="30%">Rating Criteria</th>
                                        <th width="15%">Score Achieved</th>
                                        <th width="15%">Maximum Score</th>
                                        <th width="10%">Percentage</th>
                                        <th width="25%">Justification/Remarks</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($rating_summary['ratings'] as $rating): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <strong><?= esc($rating['item_label'] ?? 'N/A') ?></strong>
                                                <?php if (!empty($rating['item_description'])): ?>
                                                    <br>
                                                    <small class="text-muted"><?= esc($rating['item_description']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?= number_format($rating['score_achieved'], 1) ?>
                                            </td>
                                            <td class="text-center">
                                                <?= number_format($rating['score_max'], 1) ?>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $itemPercentage = $rating['score_max'] > 0 ?
                                                    round(($rating['score_achieved'] / $rating['score_max']) * 100, 1) : 0;
                                                ?>
                                                <?= $itemPercentage ?>%
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= esc($rating['justification'] ?? 'No remarks provided') ?>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="2" class="text-end">Total:</th>
                                        <th class="text-center">
                                            <strong><?= number_format($rating_summary['total_achieved'], 1) ?></strong>
                                        </th>
                                        <th class="text-center">
                                            <strong><?= number_format($rating_summary['total_max'], 1) ?></strong>
                                        </th>
                                        <th class="text-center">
                                            <strong><?= number_format($rating_summary['percentage'], 1) ?>%</strong>
                                        </th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Rating Remarks -->
    <?php if (!empty($application['rating_remarks'])): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments me-2"></i>
                        Overall Rating Remarks
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-light border-left-info">
                        <p class="mb-0"><?= nl2br(esc($application['rating_remarks'])) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Hidden form for PDF export -->
<form id="pdfExportForm" method="post" action="<?= base_url('reports/scoring/applications/export') ?>" style="display: none;">
    <?= csrf_field() ?>
    <input type="hidden" name="application_id" value="<?= $application['id'] ?>">
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function exportScoringDetailPDF() {
    // Show loading state
    const exportBtn = document.querySelector('button[onclick="exportScoringDetailPDF()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating PDF...';
    exportBtn.disabled = true;

    try {
        // Submit the form to trigger PDF download
        document.getElementById('pdfExportForm').submit();

        // Reset button after a short delay
        setTimeout(() => {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }, 2000);

    } catch (error) {
        console.error('PDF Export Error:', error);
        alert('Failed to generate PDF. Please try again.');

        // Reset button
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    }
}
</script>
<?= $this->endSection() ?>
