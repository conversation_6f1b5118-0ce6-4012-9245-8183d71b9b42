<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * RateItemsScoresModel
 *
 * Model for the rate_items_scores table
 */
class RateItemsScoresModel extends Model
{
    protected $table         = 'rate_items_scores';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'item_id',
        'score',
        'label',
        'score_description',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'item_id' => 'required|numeric',
        'score' => 'required|numeric',
        'label' => 'required|max_length[100]',
        'score_description' => 'permit_empty|max_length[255]'
    ];

    protected $validationMessages = [
        'item_id' => [
            'required' => 'Item ID is required',
            'numeric'  => 'Item ID must be a number'
        ],
        'score' => [
            'required' => 'Score is required',
            'numeric'  => 'Score must be a number'
        ],
        'label' => [
            'required' => 'Label is required',
            'max_length' => 'Label cannot exceed 100 characters'
        ],
        'score_description' => [
            'max_length' => 'Score description cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get score by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getScoreById($id)
    {
        return $this->find($id);
    }

    /**
     * Get scores by item ID
     *
     * @param int $itemId
     * @return array
     */
    public function getScoresByItemId($itemId)
    {
        return $this->where('item_id', $itemId)
                    ->orderBy('score', 'ASC')
                    ->findAll();
    }

    /**
     * Get scores with item information
     *
     * @param int $itemId
     * @return array
     */
    public function getScoresWithItemInfo($itemId = null)
    {
        $builder = $this->select('rate_items_scores.*, rate_items.item_label')
                        ->join('rate_items', 'rate_items_scores.item_id = rate_items.id', 'left');
        
        if ($itemId !== null) {
            $builder->where('rate_items_scores.item_id', $itemId);
        }
        
        return $builder->orderBy('rate_items.item_label', 'ASC')
                       ->orderBy('rate_items_scores.score', 'ASC')
                       ->findAll();
    }

    /**
     * Get score statistics for an item
     *
     * @param int $itemId
     * @return array
     */
    public function getScoreStatistics($itemId)
    {
        $stats = $this->select('
            COUNT(*) as total_scores,
            MIN(score) as min_score,
            MAX(score) as max_score,
            AVG(score) as avg_score,
            SUM(score) as total_score
        ')
        ->where('item_id', $itemId)
        ->first();
        
        return [
            'total_scores' => $stats['total_scores'] ?? 0,
            'min_score' => $stats['min_score'] ?? 0,
            'max_score' => $stats['max_score'] ?? 0,
            'avg_score' => round($stats['avg_score'] ?? 0, 2),
            'total_score' => $stats['total_score'] ?? 0
        ];
    }

    /**
     * Get all score statistics
     *
     * @return array
     */
    public function getAllScoreStatistics()
    {
        $stats = [];
        
        // Total scores
        $stats['total'] = $this->countAllResults(false);
        
        // Scores with descriptions
        $stats['with_descriptions'] = $this->where('score_description IS NOT NULL')
                                           ->where('score_description !=', '')
                                           ->countAllResults(false);
        
        // Score distribution
        $distribution = $this->select('score, COUNT(*) as count')
                             ->groupBy('score')
                             ->orderBy('score', 'ASC')
                             ->findAll();
        
        $stats['distribution'] = [];
        foreach ($distribution as $dist) {
            $stats['distribution'][$dist['score']] = $dist['count'];
        }
        
        // Overall statistics
        $overall = $this->select('
            MIN(score) as min_score,
            MAX(score) as max_score,
            AVG(score) as avg_score
        ')->first();
        
        $stats['overall'] = [
            'min_score' => $overall['min_score'] ?? 0,
            'max_score' => $overall['max_score'] ?? 0,
            'avg_score' => round($overall['avg_score'] ?? 0, 2)
        ];
        
        return $stats;
    }

    /**
     * Get scores by range
     *
     * @param int $minScore
     * @param int $maxScore
     * @return array
     */
    public function getScoresByRange($minScore, $maxScore)
    {
        return $this->where('score >=', $minScore)
                    ->where('score <=', $maxScore)
                    ->orderBy('score', 'ASC')
                    ->findAll();
    }

    /**
     * Get highest scores
     *
     * @param int $limit
     * @return array
     */
    public function getHighestScores($limit = 10)
    {
        return $this->select('rate_items_scores.*, rate_items.item_label')
                    ->join('rate_items', 'rate_items_scores.item_id = rate_items.id', 'left')
                    ->orderBy('rate_items_scores.score', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get lowest scores
     *
     * @param int $limit
     * @return array
     */
    public function getLowestScores($limit = 10)
    {
        return $this->select('rate_items_scores.*, rate_items.item_label')
                    ->join('rate_items', 'rate_items_scores.item_id = rate_items.id', 'left')
                    ->orderBy('rate_items_scores.score', 'ASC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Delete scores by item ID
     *
     * @param int $itemId
     * @param int $deletedBy
     * @return bool
     */
    public function deleteScoresByItemId($itemId, $deletedBy = null)
    {
        if ($deletedBy !== null) {
            $this->where('item_id', $itemId)
                 ->set('deleted_by', $deletedBy);
        }
        
        return $this->where('item_id', $itemId)->delete(null, true);
    }

    /**
     * Update score description
     *
     * @param int $id
     * @param string $description
     * @param int $updatedBy
     * @return bool
     */
    public function updateScoreDescription($id, $description, $updatedBy = null)
    {
        $data = ['score_description' => $description];
        if ($updatedBy !== null) {
            $data['updated_by'] = $updatedBy;
        }
        
        return $this->update($id, $data);
    }
}
