<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Application Rating</h1>
            <p class="text-muted">Rate applications for the selected position</p>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>">Rating</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('rating/position-groups/' . $position['exercise_id']) ?>">Position Groups</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('rating/positions/' . $position['position_group_id']) ?>">Positions</a></li>
                <li class="breadcrumb-item active">Applications</li>
            </ol>
        </nav>
    </div>

    <!-- Position Info -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-1"><?= esc($position['designation']) ?></h5>
                    <p class="text-muted mb-0">
                        <strong>Classification:</strong> <?= esc($position['classification']) ?> | 
                        <strong>Location:</strong> <?= esc($position['location']) ?> |
                        <strong>Exercise:</strong> <?= esc($position['exercise_name']) ?>
                    </p>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('rating/positions/' . $position['position_group_id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-users"></i> Applications Ready for Rating
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Applications Ready for Rating</h5>
                    <p class="text-muted">There are no applications ready for rating for this position. Applications must have profile_status = 'profiled' before they can be rated.</p>
                    <a href="<?= base_url('rating/positions/' . $position['position_group_id']) ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Positions
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Application No.</th>
                                <th>Applicant Name</th>
                                <th>Email</th>
                                <th>Applied Date</th>
                                <th>Rating Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $application): ?>
                                <tr>
                                    <td>
                                        <strong><?= esc($application['application_number']) ?></strong>
                                    </td>
                                    <td>
                                        <?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?>
                                    </td>
                                    <td><?= esc($application['email_address']) ?></td>
                                    <td><?= date('M d, Y', strtotime($application['created_at'])) ?></td>
                                    <td>
                                        <?php if ($application['rating_status'] === 'completed'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> Rated
                                            </span>
                                            <?php if (!empty($application['rating_capability_max'])): ?>
                                                <br><small class="text-muted">Score: <?= $application['rating_capability_max'] ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock"></i> Pending
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($application['rating_status'] === 'completed'): ?>
                                            <a href="<?= base_url('rating/rate/' . $application['id']) ?>" 
                                               class="btn btn-success btn-sm">
                                                <i class="fas fa-eye"></i> View Rating
                                            </a>
                                        <?php else: ?>
                                            <a href="<?= base_url('rating/rate/' . $application['id']) ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-star"></i> Rate Application
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Rating Summary -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">Rating Summary</h6>
                                <?php 
                                    $totalApplications = count($applications);
                                    $ratedApplications = 0;
                                    foreach ($applications as $app) {
                                        if ($app['rating_status'] === 'completed') {
                                            $ratedApplications++;
                                        }
                                    }
                                    $pendingApplications = $totalApplications - $ratedApplications;
                                    $completionPercentage = $totalApplications > 0 ? round(($ratedApplications / $totalApplications) * 100) : 0;
                                ?>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <h4 class="text-primary"><?= $totalApplications ?></h4>
                                        <small class="text-muted">Total Applications</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-success"><?= $ratedApplications ?></h4>
                                        <small class="text-muted">Rated</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-warning"><?= $pendingApplications ?></h4>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-info"><?= $completionPercentage ?>%</h4>
                                        <small class="text-muted">Completion</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="card mt-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="mb-1">
                        <i class="fas fa-route text-info"></i> Navigation Path
                    </h6>
                    <p class="text-muted mb-0">
                        Dashboard → Rating → Position Groups → Positions → <strong>Applications</strong>
                    </p>
                </div>
                <div class="col-auto">
                    <small class="text-muted">Step 4 of 4</small>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
