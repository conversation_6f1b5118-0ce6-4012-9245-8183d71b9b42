<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Application Rating</h1>
            <p class="text-muted">Select a position to rate applications</p>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>">Rating</a></li>
                <li class="breadcrumb-item"><a href="<?= base_url('rating/position-groups/' . $positionGroup['exercise_id']) ?>">Position Groups</a></li>
                <li class="breadcrumb-item active">Positions</li>
            </ol>
        </nav>
    </div>

    <!-- Position Group Info -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-1"><?= esc($positionGroup['group_name']) ?></h5>
                    <p class="text-muted mb-0">
                        <strong>Exercise:</strong> <?= esc($positionGroup['exercise_name']) ?> | 
                        <strong>Advertisement No:</strong> <?= esc($positionGroup['advertisement_no']) ?> |
                        <strong>Organization:</strong> <?= esc($positionGroup['org_name']) ?>
                    </p>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('rating/position-groups/' . $positionGroup['exercise_id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Position Groups
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-briefcase"></i> Select Position
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Positions Found</h5>
                    <p class="text-muted">There are no positions available in this position group.</p>
                    <a href="<?= base_url('rating/position-groups/' . $positionGroup['exercise_id']) ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Position Groups
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Position Title</th>
                                <th>Classification</th>
                                <th>Location</th>
                                <th>Applications</th>
                                <th>Rating Progress</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td>
                                        <strong><?= esc($position['designation']) ?></strong>
                                    </td>
                                    <td><?= esc($position['classification']) ?></td>
                                    <td><?= esc($position['location']) ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $position['application_count'] ?> Application<?= $position['application_count'] != 1 ? 's' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($position['application_count'] > 0): ?>
                                            <?php 
                                                $ratedCount = $position['rated_count'];
                                                $totalCount = $position['application_count'];
                                                $percentage = $totalCount > 0 ? round(($ratedCount / $totalCount) * 100) : 0;
                                            ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar <?= $percentage == 100 ? 'bg-success' : 'bg-warning' ?>" 
                                                     role="progressbar" 
                                                     style="width: <?= $percentage ?>%"
                                                     aria-valuenow="<?= $percentage ?>" 
                                                     aria-valuemin="0" 
                                                     aria-valuemax="100">
                                                    <?= $ratedCount ?>/<?= $totalCount ?> (<?= $percentage ?>%)
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">No applications</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($position['application_count'] > 0): ?>
                                            <a href="<?= base_url('rating/applications/' . $position['id']) ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-star"></i> Rate Applications
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No applications</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="card mt-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="mb-1">
                        <i class="fas fa-route text-info"></i> Navigation Path
                    </h6>
                    <p class="text-muted mb-0">
                        Dashboard → Rating → Position Groups → <strong>Positions</strong> → Applications
                    </p>
                </div>
                <div class="col-auto">
                    <small class="text-muted">Step 3 of 4</small>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
